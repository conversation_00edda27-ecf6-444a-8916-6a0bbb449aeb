use terminus_ui::prelude::*;

// Example of basic Props validation (backward compatible)
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct BasicUserCardProps {
    pub name: String,
    pub age: u32,
    pub email: Option<String>,
}

// Example of enhanced ValidatedProps with compile-time validation
#[derive(ValidatedProps, Debug, Clone)]
pub struct ValidatedUserCardProps {
    pub name: String,
    pub age: u32,
    pub email: Option<String>,
    pub is_active: bool,
}

// Example of props with validation attributes (future enhancement)
// #[derive(ValidatedProps, Debug, Clone, Default)]
// pub struct AdvancedUserProps {
//     #[validate(length(min = 1, max = 50))]
//     pub name: String,
//
//     #[validate(range(min = 0, max = 120))]
//     pub age: u32,
//
//     #[validate(pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")]
//     pub email: Option<String>,
//
//     #[validate(required)]
//     pub department: Option<String>, // Required despite being Option
// }

// Component using basic props
#[component(BasicUserCard)]
fn basic_user_card(props: BasicUserCardProps) -> Element {
    rsx! {
        <Block
            title={format!("User: {}", props.name)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("Name: {}", props.name)} />
                <Text content={format!("Age: {}", props.age)} />
                <Text content={format!("Email: {}",
                    props.email.as_deref().unwrap_or("Not provided"))} />
            </Layout>
        </Block>
    }
}

// Component using validated props
#[component(ValidatedUserCard)]
fn validated_user_card(props: ValidatedUserCardProps) -> Element {
    let status_color = if props.is_active {
        Color::Green
    } else {
        Color::Red
    };
    let status_text = if props.is_active {
        "Active"
    } else {
        "Inactive"
    };

    rsx! {
        <Block
            title={format!("Validated User: {}", props.name)}
            borders={Borders::ALL}
            border_style={Style::default().fg(status_color)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("Name: {}", props.name)} />
                <Text content={format!("Age: {}", props.age)} />
                <Text content={format!("Email: {}",
                    props.email.as_deref().unwrap_or("Not provided"))} />
                <Text content={format!("Status: {}", status_text)} />
            </Layout>
        </Block>
    }
}

// Dashboard component demonstrating validation
#[derive(Props, Debug, Clone)]
pub struct ValidationDashboardProps {
    pub title: String,
}

#[component(ValidationDashboard)]
fn validation_dashboard(props: ValidationDashboardProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Text content="Component Props Validation Demo" />
            </Block>

            // Content area with examples
            <Layout direction={Direction::Horizontal}>
                // Basic props example
                <BasicUserCard
                    name={"Alice Johnson".to_string()}
                    age={28}
                    email={Some("<EMAIL>".to_string())}
                />

                // Validated props example
                <ValidatedUserCard
                    name={"Bob Smith".to_string()}
                    age={35}
                    email={Some("<EMAIL>".to_string())}
                    is_active={true}
                />
            </Layout>

            // Footer with validation info
            <Block
                title="Validation Features"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="✓ Compile-time props type validation\n✓ Field existence checking\n✓ Runtime validation support\n✓ Clear error messages" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Test basic props validation
    let basic_props = BasicUserCardProps {
        name: "Test User".to_string(),
        age: 25,
        email: Some("<EMAIL>".to_string()),
    };

    // Validate basic props
    match basic_props.validate() {
        Ok(()) => println!("Basic props validation passed"),
        Err(e) => println!("Basic props validation failed: {}", e),
    }

    // Test validated props
    let validated_props = ValidatedUserCardProps {
        name: "Validated User".to_string(),
        age: 30,
        email: Some("<EMAIL>".to_string()),
        is_active: true,
    };

    // Validate enhanced props using ValidatedProps trait
    match <ValidatedUserCardProps as ValidatedProps>::validate(&validated_props) {
        Ok(()) => println!("Validated props validation passed"),
        Err(errors) => {
            println!("Validated props validation failed:");
            for error in errors {
                println!("  - {}", error);
            }
        }
    }

    // Create the dashboard
    let dashboard_props = ValidationDashboardProps {
        title: "Props Validation Demo".to_string(),
    };

    let element = rsx! {
        <ValidationDashboard
            title={dashboard_props.title}
        />
    };

    render(element)
}

// Compile-time validation tests (these would fail compilation if validation is broken)
#[cfg(test)]
mod compile_time_tests {
    use super::*;

    #[test]
    fn test_basic_props_compilation() {
        // This should compile fine
        let _props = BasicUserCardProps {
            name: "Test".to_string(),
            age: 25,
            email: None,
        };
    }

    #[test]
    fn test_validated_props_compilation() {
        // This should compile fine
        let _props = ValidatedUserCardProps {
            name: "Test".to_string(),
            age: 25,
            email: None,
            is_active: true,
        };
    }

    #[test]
    fn test_component_props_validation() {
        // This should compile fine - component accepts correct props type
        let props = BasicUserCardProps::default();
        let _element = BasicUserCard::create_typed_element(props);
    }

    // Uncomment these to test compile-time failures:

    // #[test]
    // fn test_wrong_props_type() {
    //     // This should fail compilation - wrong props type
    //     let props = ValidatedUserCardProps::default();
    //     let _element = BasicUserCardComponent::create_typed_element(props);
    // }

    // #[test]
    // fn test_missing_required_field() {
    //     // This should fail compilation - missing required field
    //     let _element = rsx! {
    //         <BasicUserCard
    //             name={"Test".to_string()}
    //             // age is missing - should cause compilation error
    //         />
    //     };
    // }
}
