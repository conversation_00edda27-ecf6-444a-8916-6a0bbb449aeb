/// Example demonstrating the ScrollArea component
/// This showcases scrollable content with customizable scrollbars
use terminus_ui::prelude::*;

/// Props for the main demo app
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct ScrollDemoAppProps {
    pub title: String,
}

/// Main demo app component
#[component(ScrollDemoApp)]
fn scroll_demo_app(props: ScrollDemoAppProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="ScrollArea Demo - Use arrow keys, Page Up/Down, Home/End, or mouse wheel to scroll" />
            </Block>

            // Main content area with ScrollArea
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),  // Left panel
                    Constraint::Percentage(50),  // Right panel
                ]}
            >
                // Left panel - Vertical scrolling demo
                <Block
                    title="Vertical Scrolling"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green)}
                >
                    <ScrollArea>
                        <Text content="Line 1: This is a long line of text that demonstrates vertical scrolling capabilities." />
                        <Text content="Line 2: The ScrollArea component automatically handles content that exceeds the viewport." />
                        <Text content="Line 3: You can use arrow keys to scroll up and down through the content." />
                        <Text content="Line 4: Page Up and Page Down keys scroll by viewport height." />
                        <Text content="Line 5: Home and End keys jump to the beginning and end of content." />
                        <Text content="Line 6: Mouse wheel scrolling is also supported for smooth navigation." />
                        <Text content="Line 7: The scrollbar appears automatically when content is larger than viewport." />
                        <Text content="Line 8: The scrollbar thumb size reflects the ratio of viewport to content size." />
                        <Text content="Line 9: Scrollbar position can be customized (left, right for vertical)." />
                        <Text content="Line 10: Custom symbols and styling can be applied to scrollbars." />
                        <Text content="Line 11: This component follows Shadcn UI design patterns." />
                        <Text content="Line 12: It integrates seamlessly with the terminus-ui component system." />
                        <Text content="Line 13: Performance is optimized for large content areas." />
                        <Text content="Line 14: Focus management ensures proper keyboard navigation." />
                        <Text content="Line 15: The API is designed to be intuitive and React-like." />
                        <Text content="Line 16: Error handling covers edge cases gracefully." />
                        <Text content="Line 17: The component is fully type-safe with Rust's type system." />
                        <Text content="Line 18: Documentation includes comprehensive examples." />
                        <Text content="Line 19: Testing ensures reliability across different scenarios." />
                        <Text content="Line 20: This is the last line of the scrollable content." />
                    </ScrollArea>
                </Block>

                // Right panel - Horizontal scrolling demo
                <Block
                    title="Horizontal Scrolling"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Magenta)}
                >
                    <ScrollArea
                        show_vertical_scrollbar={Some(false)}
                        horizontal_scrollbar_position={Some(ScrollbarPosition::Top)}
                    >
                        <Text content="This is a very long line of text that extends far beyond the normal viewport width and demonstrates horizontal scrolling capabilities in the ScrollArea component. You can use left and right arrow keys to scroll horizontally through this content." />
                        <Text content="Another long line that shows how multiple lines can be scrolled horizontally. The horizontal scrollbar appears at the top of the content area as configured in the component props." />
                        <Text content="The third line continues the demonstration of horizontal scrolling with even more text content that requires scrolling to view completely. This showcases the flexibility of the ScrollArea component." />
                    </ScrollArea>
                </Block>
            </Layout>

            // Bottom panel - Custom scrollbar demo
            <Block
                title="Custom Scrollbar Styling"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <ScrollArea
                    vertical_scrollbar_position={Some(ScrollbarPosition::Left)}
                    vertical_scrollbar_appearance={Some(ScrollbarAppearance {
                        begin_symbol: "↑".to_string(),
                        end_symbol: "↓".to_string(),
                        track_symbol: "┃".to_string(),
                        thumb_symbol: "█".to_string(),
                        track_style: Some(Style::default().fg(Color::DarkGray)),
                        thumb_style: Some(Style::default().fg(Color::White).bg(Color::Blue)),
                        symbol_style: Some(Style::default().fg(Color::Cyan)),
                    })}
                >
                    <Text content="Custom scrollbar with different symbols and colors" />
                    <Text content="The scrollbar is positioned on the left side" />
                    <Text content="Track uses ┃ symbol with dark gray color" />
                    <Text content="Thumb uses █ symbol with white on blue styling" />
                    <Text content="Begin/end symbols use ↑/↓ with cyan color" />
                    <Text content="This demonstrates the full customization capabilities" />
                    <Text content="You can create scrollbars that match your application's theme" />
                    <Text content="The styling system is flexible and powerful" />
                </ScrollArea>
            </Block>

            // Footer
            <Block
                title="Instructions"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Red)}
            >
                <Text content="Controls: ↑↓←→ (scroll), PgUp/PgDn (page scroll), Home/End (jump), Mouse wheel (smooth scroll), 'q' or Esc (quit)" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <ScrollDemoApp title={"ScrollArea Component Demo".to_string()} />
    };

    render(element)
}
