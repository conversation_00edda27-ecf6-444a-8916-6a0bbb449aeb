use std::time::{SystemTime, UNIX_EPOCH};
use terminus_ui::prelude::*;

/// Example demonstrating React-style context API usage
/// This showcases how to share state between components without prop drilling

// Define context types for different kinds of shared state

#[derive(Clone, Debug, PartialEq)]
struct ThemeContext {
    primary_color: Color,
    secondary_color: Color,
    background_color: Color,
    text_style: Style,
}

impl Default for ThemeContext {
    fn default() -> Self {
        Self {
            primary_color: Color::Blue,
            secondary_color: Color::Cyan,
            background_color: Color::Black,
            text_style: Style::default().fg(Color::White),
        }
    }
}

#[derive(Clone, Debug, PartialEq)]
struct UserContext {
    username: String,
    role: String,
    permissions: Vec<String>,
    login_time: u64,
}

impl Default for UserContext {
    fn default() -> Self {
        Self {
            username: "Guest".to_string(),
            role: "Visitor".to_string(),
            permissions: vec!["read".to_string()],
            login_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        }
    }
}

#[derive(<PERSON>lone, Debug, PartialEq)]
struct AppStateContext {
    app_name: String,
    version: String,
    debug_mode: bool,
    feature_flags: Vec<String>,
}

impl Default for AppStateContext {
    fn default() -> Self {
        Self {
            app_name: "Terminus UI Demo".to_string(),
            version: "1.0.0".to_string(),
            debug_mode: true,
            feature_flags: vec!["context_api".to_string(), "hooks".to_string()],
        }
    }
}

// Create default contexts
static DEFAULT_THEME: std::sync::LazyLock<Context<ThemeContext>> =
    std::sync::LazyLock::new(|| create_context_with_default(ThemeContext::default()));

static DEFAULT_USER: std::sync::LazyLock<Context<UserContext>> =
    std::sync::LazyLock::new(|| create_context_with_default(UserContext::default()));

static DEFAULT_APP_STATE: std::sync::LazyLock<Context<AppStateContext>> =
    std::sync::LazyLock::new(|| create_context_with_default(AppStateContext::default()));

// Theme provider component
#[derive(Props, Debug, Clone)]
struct ThemeProviderProps {
    pub title: String,
}

#[component(ThemeProvider)]
fn theme_provider(props: ThemeProviderProps) -> Element {
    let (theme_mode, _set_theme_mode) = use_state("Dark".to_string());

    // Provide different themes based on the current mode
    let theme = use_context_provider(|| match theme_mode.get().as_str() {
        "Dark" => ThemeContext {
            primary_color: Color::Magenta,
            secondary_color: Color::Cyan,
            background_color: Color::Black,
            text_style: Style::default().fg(Color::White),
        },
        "Light" => ThemeContext {
            primary_color: Color::Blue,
            secondary_color: Color::LightBlue,
            background_color: Color::White,
            text_style: Style::default().fg(Color::Black),
        },
        "High Contrast" => ThemeContext {
            primary_color: Color::Yellow,
            secondary_color: Color::White,
            background_color: Color::Black,
            text_style: Style::default()
                .fg(Color::Yellow)
                .add_modifier(Modifier::BOLD),
        },
        _ => ThemeContext::default(),
    });

    let theme_mode_value = theme_mode.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(theme.primary_color)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("🎨 Current Theme: {}", theme_mode_value)} />
                <Text content={format!("Primary: {:?}", theme.primary_color)} />
                <Text content={format!("Secondary: {:?}", theme.secondary_color)} />
                <Text content="This component provides theme context to children" />
                <Text content="Press 't' to cycle themes" />
            </Layout>
        </Block>
    }
}

// User provider component
#[derive(Props, Debug, Clone)]
struct UserProviderProps {
    pub title: String,
}

#[component(UserProvider)]
fn user_provider(props: UserProviderProps) -> Element {
    let (user_type, _set_user_type) = use_state("Admin".to_string());

    // Provide different user contexts based on the current user type
    let user = use_context_provider(|| match user_type.get().as_str() {
        "Admin" => UserContext {
            username: "admin".to_string(),
            role: "Administrator".to_string(),
            permissions: vec![
                "read".to_string(),
                "write".to_string(),
                "delete".to_string(),
                "admin".to_string(),
            ],
            login_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        },
        "User" => UserContext {
            username: "user".to_string(),
            role: "Standard User".to_string(),
            permissions: vec!["read".to_string(), "write".to_string()],
            login_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        },
        "Guest" => UserContext::default(),
        _ => UserContext::default(),
    });

    let user_type_value = user_type.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("👤 Current User: {}", user_type_value)} />
                <Text content={format!("Username: {}", user.username)} />
                <Text content={format!("Role: {}", user.role)} />
                <Text content={format!("Permissions: {}", user.permissions.len())} />
                <Text content="This component provides user context to children" />
                <Text content="Press 'u' to cycle user types" />
            </Layout>
        </Block>
    }
}

// Theme consumer component
#[derive(Props, Debug, Clone)]
struct ThemeConsumerProps {
    pub title: String,
}

#[component(ThemeConsumer)]
fn theme_consumer(props: ThemeConsumerProps) -> Element {
    // Consume theme context with default fallback
    let theme = use_context_with_default(&DEFAULT_THEME);

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(theme.secondary_color)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content="🎨 Theme Consumer Component" style={theme.text_style} />
                <Text content={format!("Using Primary: {:?}", theme.primary_color)} style={theme.text_style} />
                <Text content={format!("Using Secondary: {:?}", theme.secondary_color)} style={theme.text_style} />
                <Text content="This component consumes theme context" style={theme.text_style} />
            </Layout>
        </Block>
    }
}

// User consumer component
#[derive(Props, Debug, Clone)]
struct UserConsumerProps {
    pub title: String,
}

#[component(UserConsumer)]
fn user_consumer(props: UserConsumerProps) -> Element {
    // Consume user context with default fallback
    let user = use_context_with_default(&DEFAULT_USER);

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content="👤 User Consumer Component" />
                <Text content={format!("Welcome, {}!", user.username)} />
                <Text content={format!("Role: {}", user.role)} />
                <Text content={format!("Permissions: {:?}", user.permissions)} />
                <Text content="This component consumes user context" />
            </Layout>
        </Block>
    }
}

// Combined consumer component that uses multiple contexts
#[derive(Props, Debug, Clone)]
struct CombinedConsumerProps {
    pub title: String,
}

#[component(CombinedConsumer)]
fn combined_consumer(props: CombinedConsumerProps) -> Element {
    // Consume multiple contexts
    let theme = use_context_with_default(&DEFAULT_THEME);
    let user = use_context_with_default(&DEFAULT_USER);
    let app_state = use_context_with_default(&DEFAULT_APP_STATE);

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(theme.primary_color)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content="🔗 Combined Context Consumer" style={theme.text_style} />
                <Text content={format!("App: {} v{}", app_state.app_name, app_state.version)} style={theme.text_style} />
                <Text content={format!("User: {} ({})", user.username, user.role)} style={theme.text_style} />
                <Text content={format!("Theme: {:?} on {:?}", theme.primary_color, theme.background_color)} style={theme.text_style} />
                <Text content={format!("Debug Mode: {}", app_state.debug_mode)} style={theme.text_style} />
                <Text content="This component uses multiple contexts" style={theme.text_style} />
            </Layout>
        </Block>
    }
}

// Main application component
#[derive(Props, Debug, Clone)]
struct ContextDemoAppProps {
    pub title: String,
}

#[component(ContextDemoApp)]
fn context_demo_app(props: ContextDemoAppProps) -> Element {
    // Provide app state context at the root level
    let _app_state = use_context_provider(AppStateContext::default);

    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="🚀 React-style Context API Demo for Terminus UI" />
                    <Text content="This demo showcases context providers and consumers" />
                    <Text content="Context allows sharing state without prop drilling" />
                </Layout>
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),
                    Constraint::Percentage(50),
                ]}
            >
                <ThemeProvider title={"🎨 Theme Provider".to_string()} />
                <UserProvider title={"👤 User Provider".to_string()} />
            </Layout>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(33),
                    Constraint::Percentage(33),
                    Constraint::Percentage(34),
                ]}
            >
                <ThemeConsumer title={"🎨 Theme Consumer".to_string()} />
                <UserConsumer title={"👤 User Consumer".to_string()} />
                <CombinedConsumer title={"🔗 Multi-Context".to_string()} />
            </Layout>

            <Block
                title="📋 Context API Features Demonstrated"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="✅ Context Providers - Share state down the component tree" />
                    <Text content="✅ Context Consumers - Access shared state from any child component" />
                    <Text content="✅ Default Contexts - Provide fallback values when no provider exists" />
                    <Text content="✅ Multiple Contexts - Use different context types in the same component" />
                    <Text content="✅ Type Safety - Compile-time type checking for context values" />
                    <Text content="✅ React Parity - Familiar API for React developers" />
                    <Text content="✅ Hook Integration - Works seamlessly with useState and useEffect" />
                    <Text content="✅ No Prop Drilling - Avoid passing props through intermediate components" />
                </Layout>
            </Block>
        </Layout>
    }
}

// Main function
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚀 Starting Context API Demo...");

    let app_props = ContextDemoAppProps {
        title: "🔗 use_context Hook Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <ContextDemoApp title={app_props.title} />
    };

    println!("🎯 Running comprehensive context API demo...");
    println!("📝 This demo showcases React-style context management:");
    println!("   - Context providers for sharing state");
    println!("   - Context consumers for accessing shared state");
    println!("   - Default contexts with fallback values");
    println!("   - Multiple context types in single components");
    println!("   - Type-safe context value handling");
    println!("   - Integration with useState hooks");

    // Run the UI
    render_async(element).await?;

    println!("✅ Context API demo completed!");
    Ok(())
}
