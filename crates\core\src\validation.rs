use std::fmt;

use crate::ComponentProps;

/// Validation error that can be used at compile time and runtime
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct ValidationError {
    pub field_name: String,
    pub error_message: String,
    pub component_name: Option<String>,
}

impl fmt::Display for ValidationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        if let Some(component) = &self.component_name {
            write!(
                f,
                "Validation error in component '{}', field '{}': {}",
                component, self.field_name, self.error_message
            )
        } else {
            write!(
                f,
                "Validation error in field '{}': {}",
                self.field_name, self.error_message
            )
        }
    }
}

impl std::error::Error for ValidationError {}

/// Validation rule metadata for compile-time checking
#[derive(Debug, Clone)]
pub struct ValidationRule {
    pub rule_type: ValidationRuleType,
    pub parameters: Vec<String>,
    pub error_message: Option<String>,
}

/// Types of validation rules supported
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub enum ValidationRuleType {
    Required,
    Range {
        min: Option<i64>,
        max: Option<i64>,
    },
    Length {
        min: Option<usize>,
        max: Option<usize>,
    },
    Pattern(String),
    Custom(String),     // Function name for custom validation
    OneOf(Vec<String>), // Enum-like validation
}

/// Enhanced field metadata with validation rules
#[derive(Debug, Clone)]
pub struct ValidatedFieldMetadata {
    pub name: String,
    pub type_name: String,
    pub is_optional: bool,
    pub default_value: Option<String>,
    pub validation_rules: Vec<ValidationRule>,
}

/// Trait for validatable props with compile-time guarantees
pub trait ValidatedProps: 'static + Clone + Send + Sync + std::fmt::Debug {
    /// Get validation metadata for all fields
    fn validation_metadata() -> Vec<ValidatedFieldMetadata>;

    /// Validate the props at runtime (fallback for complex validation)
    fn validate(&self) -> Result<(), Vec<ValidationError>> {
        Ok(())
    }

    /// Get the component name for better error messages
    fn component_name() -> &'static str {
        std::any::type_name::<Self>()
    }
}

/// Macro helper for generating validation code
pub struct ValidationCodeGenerator;

impl ValidationCodeGenerator {
    /// Generate compile-time validation for required fields
    pub fn generate_required_field_check(
        field_name: &str,
        field_type: &str,
        field_id: u32,
    ) -> String {
        if field_type.starts_with("Option<") {
            format!(
                r#"
                // Field '{}' is optional, no required check needed
                "#,
                field_name
            )
        } else {
            format!(
                r#"
                // Compile-time check: field '{}' is required
                const _: () = {{
                    fn _check_required_field_{}_exists<T>()
                    where
                        T: HasField<FieldName<{}>>,
                    {{}}
                    _check_required_field_{}_exists::<Self>();
                }};
                "#,
                field_name, field_name, field_id, field_name
            )
        }
    }

    /// Generate compile-time type validation
    pub fn generate_type_check(field_name: &str, expected_type: &str, field_id: u32) -> String {
        format!(
            r#"
            // Compile-time type check for field '{}'
            const _: () = {{
                fn _check_field_{}_type<T>()
                where
                    T: HasFieldOfType<FieldName<{}>, {}>,
                {{}}
                _check_field_{}_type::<Self>();
            }};
            "#,
            field_name, field_name, field_id, expected_type, field_name
        )
    }

    /// Generate validation for numeric ranges
    pub fn generate_range_validation(
        field_name: &str,
        min: Option<i64>,
        max: Option<i64>,
    ) -> String {
        let mut checks = Vec::new();

        if let Some(min_val) = min {
            checks.push(format!(
                "if self.{} < {} {{ errors.push(ValidationError {{ field_name: \"{}\".to_string(), error_message: \"Value must be >= {}\".to_string(), component_name: Some(Self::component_name().to_string()) }}); }}",
                field_name, min_val, field_name, min_val
            ));
        }

        if let Some(max_val) = max {
            checks.push(format!(
                "if self.{} > {} {{ errors.push(ValidationError {{ field_name: \"{}\".to_string(), error_message: \"Value must be <= {}\".to_string(), component_name: Some(Self::component_name().to_string()) }}); }}",
                field_name, max_val, field_name, max_val
            ));
        }

        if checks.is_empty() {
            String::new()
        } else {
            format!("{{ {} }}", checks.join(" "))
        }
    }

    /// Generate validation for string lengths
    pub fn generate_length_validation(
        field_name: &str,
        min: Option<usize>,
        max: Option<usize>,
    ) -> String {
        let mut checks = Vec::new();

        if let Some(min_len) = min {
            checks.push(format!(
                "if self.{}.len() < {} {{ errors.push(ValidationError {{ field_name: \"{}\".to_string(), error_message: \"Length must be >= {}\".to_string(), component_name: Some(Self::component_name().to_string()) }}); }}",
                field_name, min_len, field_name, min_len
            ));
        }

        if let Some(max_len) = max {
            checks.push(format!(
                "if self.{}.len() > {} {{ errors.push(ValidationError {{ field_name: \"{}\".to_string(), error_message: \"Length must be <= {}\".to_string(), component_name: Some(Self::component_name().to_string()) }}); }}",
                field_name, max_len, field_name, max_len
            ));
        }

        if checks.is_empty() {
            String::new()
        } else {
            format!("{{ {} }}", checks.join(" "))
        }
    }
}

/// Compile-time trait for checking field existence using phantom types
pub trait HasField<F> {}

/// Compile-time trait for checking field type using phantom types
pub trait HasFieldOfType<F, T> {}

/// Phantom type for field names
pub struct FieldName<const N: u32>;

/// Helper macro for implementing field existence checks
#[macro_export]
macro_rules! impl_has_field {
    ($struct_name:ident, $field_name:ident, $field_id:expr) => {
        impl HasField<FieldName<$field_id>> for $struct_name {}
    };
}

/// Helper macro for implementing field type checks
#[macro_export]
macro_rules! impl_has_field_of_type {
    ($struct_name:ident, $field_name:ident, $field_type:ty, $field_id:expr) => {
        impl HasFieldOfType<FieldName<$field_id>, $field_type> for $struct_name {}
    };
}

/// Trait for compile-time prop requirement validation
pub trait PropRequirements {
    /// Get a list of required field names (non-Option fields)
    fn required_fields() -> &'static [&'static str];

    /// Get a list of all field names and their types
    fn all_fields() -> &'static [(&'static str, &'static str)];

    /// Generate a user-friendly error message for missing props
    fn missing_props_error(provided_props: &[&str]) -> String {
        let required = Self::required_fields();
        let missing: Vec<&str> = required
            .iter()
            .filter(|&&field| !provided_props.contains(&field))
            .copied()
            .collect();

        if missing.is_empty() {
            String::new()
        } else {
            let all_fields = Self::all_fields();
            let missing_with_types: Vec<String> = missing
                .iter()
                .map(|&field| {
                    let field_type = all_fields
                        .iter()
                        .find(|(name, _)| *name == field)
                        .map(|(_, ty)| *ty)
                        .unwrap_or("unknown");
                    format!("'{}' of type '{}'", field, field_type)
                })
                .collect();

            format!(
                "Missing required props for component '{}': {}. Usage: <{} {} />",
                std::any::type_name::<Self>()
                    .split("::")
                    .last()
                    .unwrap_or("Component"),
                missing_with_types.join(", "),
                std::any::type_name::<Self>()
                    .split("::")
                    .last()
                    .unwrap_or("Component")
                    .replace("Props", ""),
                missing
                    .iter()
                    .map(|field| format!("{}={{your_{}_value}}", field, field))
                    .collect::<Vec<_>>()
                    .join(" ")
            )
        }
    }
}

/// Macro to implement PropRequirements for a props struct
#[macro_export]
macro_rules! impl_prop_requirements {
    ($struct_name:ident, required: [$($req_field:literal),*], all: [$(($field:literal, $type:literal)),*]) => {
        impl PropRequirements for $struct_name {
            fn required_fields() -> &'static [&'static str] {
                &[$($req_field),*]
            }

            fn all_fields() -> &'static [(&'static str, &'static str)] {
                &[$(($field, $type)),*]
            }
        }
    };
}

/// Helper trait for compile-time prop validation
pub trait ValidateProps<T> {
    /// Validate that all required props are provided
    fn validate_required_props() -> Result<T, &'static str>;
}

/// Implementation for props that have all required fields
impl<T> ValidateProps<T> for T
where
    T: PropRequirements + Default + ComponentProps,
{
    fn validate_required_props() -> Result<T, &'static str> {
        // This will be overridden by the macro-generated implementations
        Ok(T::default())
    }
}

/// Trait for compile-time prop requirement checking
/// This trait is used to generate compile-time errors for missing required props
pub trait RequiredPropsChecker {
    /// Check that all required props are provided
    /// This method should only be callable when all required props are present
    fn check_required_props() -> Self;
}

/// Marker trait for props structs that have all required fields satisfied
pub trait AllRequiredPropsProvided {}

/// Helper trait to create props with better error messages
pub trait CreatePropsWithValidation {
    /// Create props instance, providing helpful error messages if required props are missing
    fn create_with_validation() -> Self;
}

impl<T> CreatePropsWithValidation for T
where
    T: ComponentProps + Default,
{
    fn create_with_validation() -> Self {
        T::default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validation_error_display() {
        let error = ValidationError {
            field_name: "age".to_string(),
            error_message: "Must be between 0 and 120".to_string(),
            component_name: Some("UserCard".to_string()),
        };

        let expected =
            "Validation error in component 'UserCard', field 'age': Must be between 0 and 120";
        assert_eq!(error.to_string(), expected);
    }

    #[test]
    fn test_range_validation_generation() {
        let code = ValidationCodeGenerator::generate_range_validation("age", Some(0), Some(120));
        assert!(code.contains("age"));
        assert!(code.contains("0"));
        assert!(code.contains("120"));
    }
}
