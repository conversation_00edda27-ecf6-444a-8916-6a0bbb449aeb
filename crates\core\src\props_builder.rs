use crate::{ComponentProps, PropFieldMetadata, TypedProps};
use std::any::Any;
use std::collections::HashMap;

/// A generic props builder that can construct any props type using metadata
pub struct PropsBuilder {
    props_type_name: String,
    field_metadata: Vec<PropFieldMetadata>,
    field_values: HashMap<String, Box<dyn Any + Send + Sync>>,
}

impl PropsBuilder {
    /// Create a new props builder for a specific props type
    pub fn new<P: ComponentProps>() -> Self {
        Self {
            props_type_name: P::type_name().to_string(),
            field_metadata: P::field_metadata(),
            field_values: HashMap::new(),
        }
    }

    /// Set a field value
    pub fn set_field<T: Any + Send + Sync + Clone>(&mut self, name: &str, value: T) -> &mut Self {
        self.field_values.insert(name.to_string(), Box::new(value));
        self
    }

    /// Set a field value from a string (for literal values)
    pub fn set_field_from_string(&mut self, name: &str, value: &str) -> Result<&mut Self, String> {
        // Find the field metadata
        let field_meta = self
            .field_metadata
            .iter()
            .find(|meta| meta.name == name)
            .ok_or_else(|| format!("Field '{}' not found", name))?;

        // Parse the value based on the field type
        match field_meta.type_name.as_str() {
            "String" => {
                self.field_values
                    .insert(name.to_string(), Box::new(value.to_string()));
            }
            "Option<String>" => {
                self.field_values
                    .insert(name.to_string(), Box::new(Some(value.to_string())));
            }
            "bool" => {
                let bool_val = value
                    .parse::<bool>()
                    .map_err(|_| format!("Cannot parse '{}' as bool", value))?;
                self.field_values
                    .insert(name.to_string(), Box::new(bool_val));
            }
            "Option<bool>" => {
                let bool_val = value
                    .parse::<bool>()
                    .map_err(|_| format!("Cannot parse '{}' as bool", value))?;
                self.field_values
                    .insert(name.to_string(), Box::new(Some(bool_val)));
            }
            "u32" => {
                let u32_val = value
                    .parse::<u32>()
                    .map_err(|_| format!("Cannot parse '{}' as u32", value))?;
                self.field_values
                    .insert(name.to_string(), Box::new(u32_val));
            }
            "Option<u32>" => {
                let u32_val = value
                    .parse::<u32>()
                    .map_err(|_| format!("Cannot parse '{}' as u32", value))?;
                self.field_values
                    .insert(name.to_string(), Box::new(Some(u32_val)));
            }
            _ => {
                // For complex types, store as string and let the caller handle conversion
                self.field_values
                    .insert(name.to_string(), Box::new(value.to_string()));
            }
        }

        Ok(self)
    }

    /// Get a field value
    pub fn get_field<T: Any + Clone>(&self, name: &str) -> Option<T> {
        self.field_values
            .get(name)
            .and_then(|boxed| boxed.downcast_ref::<T>())
            .cloned()
    }

    /// Check if a field has been set
    pub fn has_field(&self, name: &str) -> bool {
        self.field_values.contains_key(name)
    }

    /// Get all field names that have been set
    pub fn set_field_names(&self) -> Vec<String> {
        self.field_values.keys().cloned().collect()
    }

    /// Get the props type name
    pub fn props_type_name(&self) -> &str {
        &self.props_type_name
    }

    /// Get field metadata
    pub fn field_metadata(&self) -> &[PropFieldMetadata] {
        &self.field_metadata
    }

    /// Build the final props object (this would need to be implemented per props type)
    /// For now, we'll return a TypedProps that can be used with the component system
    pub fn build_typed_props<P: ComponentProps + Default>(&self) -> Result<TypedProps, String> {
        // This is a simplified implementation
        // In a real implementation, you'd use reflection or code generation
        // to construct the actual props struct
        let props = P::default();
        Ok(TypedProps::new(props))
    }
}

/// Trait for props types that can be built dynamically
pub trait BuildableProps: ComponentProps + Default {
    /// Apply field values from a props builder
    fn apply_from_builder(&mut self, builder: &PropsBuilder) -> Result<(), String>;
}

/// Helper function to create a props builder for a specific type
pub fn props_builder<P: ComponentProps>() -> PropsBuilder {
    PropsBuilder::new::<P>()
}

/// Helper function to build props from a map of field values
pub fn build_props_from_map<P: BuildableProps>(
    field_values: HashMap<String, String>,
) -> Result<P, String> {
    let mut builder = PropsBuilder::new::<P>();

    for (name, value) in field_values {
        builder.set_field_from_string(&name, &value)?;
    }

    let mut props = P::default();
    props.apply_from_builder(&builder)?;
    Ok(props)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[derive(Debug, Clone, Default)]
    struct TestProps {
        pub name: String,
        pub age: Option<u32>,
        pub active: bool,
    }

    impl ComponentProps for TestProps {
        fn field_metadata() -> Vec<PropFieldMetadata> {
            vec![
                PropFieldMetadata {
                    name: "name".to_string(),
                    type_name: "String".to_string(),
                    is_optional: false,
                    default_value: None,
                },
                PropFieldMetadata {
                    name: "age".to_string(),
                    type_name: "Option<u32>".to_string(),
                    is_optional: true,
                    default_value: None,
                },
                PropFieldMetadata {
                    name: "active".to_string(),
                    type_name: "bool".to_string(),
                    is_optional: false,
                    default_value: Some("false".to_string()),
                },
            ]
        }
    }

    impl BuildableProps for TestProps {
        fn apply_from_builder(&mut self, builder: &PropsBuilder) -> Result<(), String> {
            if let Some(name) = builder.get_field::<String>("name") {
                self.name = name;
            }
            if let Some(age) = builder.get_field::<Option<u32>>("age") {
                self.age = age;
            }
            if let Some(active) = builder.get_field::<bool>("active") {
                self.active = active;
            }
            Ok(())
        }
    }

    #[test]
    fn test_props_builder() {
        let mut builder = PropsBuilder::new::<TestProps>();
        builder.set_field("name", "John".to_string());
        builder.set_field("age", Some(30u32));
        builder.set_field("active", true);

        assert!(builder.has_field("name"));
        assert!(builder.has_field("age"));
        assert!(builder.has_field("active"));

        let name: Option<String> = builder.get_field("name");
        assert_eq!(name, Some("John".to_string()));
    }

    #[test]
    fn test_props_builder_from_string() {
        let mut builder = PropsBuilder::new::<TestProps>();
        builder.set_field_from_string("name", "Jane").unwrap();
        builder.set_field_from_string("active", "true").unwrap();

        let name: Option<String> = builder.get_field("name");
        assert_eq!(name, Some("Jane".to_string()));

        let active: Option<bool> = builder.get_field("active");
        assert_eq!(active, Some(true));
    }
}
