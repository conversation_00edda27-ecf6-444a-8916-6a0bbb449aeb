use crate::errors::{Component<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Component<PERSON><PERSON><PERSON>, TypeDisplay};
use crate::parsing::ComponentParams;
use syn::{FnArg, ReturnType, Type};

/// Validate component function signature and generate validation code
pub struct ComponentValidator;

impl ComponentValidator {
    /// Perform early validation of component function signature before detailed parsing
    /// This provides quick feedback for obviously invalid signatures
    pub fn early_validate_signature(
        inputs: &syn::punctuated::Punctuated<FnArg, syn::token::Comma>,
        output: &ReturnType,
    ) -> ComponentResult<()> {
        // Must have at least one parameter (props)
        if inputs.is_empty() {
            return Err(ComponentMacroError::invalid_parameter_order(
                proc_macro2::Span::call_site(),
                "Component function must have at least one parameter for props.".to_string(),
            ));
        }

        // Must return Element type
        Self::validate_return_type(output)?;

        Ok(())
    }

    /// Validate that the function returns Element type
    pub fn validate_return_type(output: &ReturnType) -> ComponentResult<()> {
        match output {
            ReturnType::Type(_, ty) => {
                if Self::is_element_type(ty) {
                    Ok(())
                } else {
                    let mut error = ComponentMacroError::invalid_return_type(
                        proc_macro2::Span::call_site(),
                        ty.display_type(),
                    );

                    // Enhance error with suggestions
                    error = Self::enhance_error_with_suggestions(error);
                    Err(error)
                }
            }
            ReturnType::Default => {
                let mut error = ComponentMacroError::invalid_return_type(
                    proc_macro2::Span::call_site(),
                    "() (unit type)".to_string(),
                );

                // Enhance error with suggestions
                error = Self::enhance_error_with_suggestions(error);
                Err(error)
            }
        }
    }

    /// Enhance error messages with specific suggestions based on error type
    /// This method now actively integrates with the suggestion system
    fn enhance_error_with_suggestions(error: ComponentMacroError) -> ComponentMacroError {
        // The error will automatically include suggestions when converted to syn::Error
        // via the to_syn_error() method which calls get_suggestions()
        // This method can be extended for additional context-specific enhancements
        error
    }

    /// Validate props type and provide detailed error messages
    pub fn validate_props_type(props_type: &Type) -> ComponentResult<()> {
        // For now, we perform basic validation and let the compiler catch trait issues
        // In the future, we could add more sophisticated type analysis here

        // Check if the props type looks reasonable (not a primitive type)
        if let Type::Path(type_path) = props_type {
            let path = &type_path.path;
            if path.segments.len() == 1 {
                let segment = &path.segments[0];
                let type_name = segment.ident.to_string();

                // Warn about common primitive types that shouldn't be used as props
                if matches!(
                    type_name.as_str(),
                    "i32" | "u32" | "f32" | "f64" | "bool" | "String" | "str"
                ) {
                    return Err(ComponentMacroError::invalid_props_type(
                        proc_macro2::Span::call_site(),
                        type_name,
                    ));
                }
            }
        }

        Ok(())
    }

    /// Generate compile-time validation code for props type
    pub fn generate_props_validation(
        params: &ComponentParams,
        component_name: &syn::Ident,
        props_type: &Type,
    ) -> ComponentResult<proc_macro2::TokenStream> {
        if params.props_param.is_some() {
            // Validate props type first
            Self::validate_props_type(props_type)?;

            Ok(quote::quote! {
                // Compile-time check: props type must implement ComponentProps
                // Using a trait bound in the impl block instead of const fn
                impl #component_name {
                    fn _validate_props_type()
                    where
                        #props_type: ComponentProps,
                    {
                        // This function will only compile if the props type implements ComponentProps
                    }
                }
            })
        } else {
            Ok(quote::quote! {})
        }
    }

    /// Validate parameter order and types are correct
    pub fn validate_parameter_order(params: &ComponentParams) -> ComponentResult<()> {
        // Basic validation - props should be present if we have any parameters
        if params.props_param.is_none()
            && (params.frame_param.is_some() || params.area_param.is_some())
        {
            return Err(ComponentMacroError::invalid_parameter_order(
                proc_macro2::Span::call_site(),
                "Props parameter is required when using frame or area parameters. Props must be the first parameter.".to_string()
            ));
        }

        Ok(())
    }

    /// Check if a type is Element using AST pattern matching
    ///
    /// Recognizes these patterns:
    /// - Element
    /// - terminus_ui::Element
    /// - ::terminus_ui::Element
    /// - crate::Element (if in terminus_ui context)
    fn is_element_type(ty: &Type) -> bool {
        match ty {
            Type::Path(type_path) => {
                let path = &type_path.path;

                // Check for simple "Element"
                if path.segments.len() == 1 {
                    return path.segments[0].ident == "Element";
                }

                // Check for qualified paths like terminus_ui::Element
                if path.segments.len() >= 2 {
                    let last_segment = &path.segments.last().unwrap().ident;
                    return last_segment == "Element";
                }

                false
            }
            _ => false,
        }
    }

    /// Validate that props type implements ComponentProps trait (compile-time check)
    pub fn validate_props_type_implements_trait(_props_type: &Type) -> ComponentResult<()> {
        // This is a compile-time check that will be generated in the output
        // We can't validate this at macro expansion time, but we can ensure
        // the generated code includes the proper trait bounds

        // For now, we assume the props type is valid and let the compiler
        // catch any trait implementation issues
        Ok(())
    }

    /// Generate comprehensive validation for the entire component signature
    pub fn validate_component_signature(
        params: &ComponentParams,
        output: &ReturnType,
        component_name: &syn::Ident,
        props_type: &Type,
    ) -> ComponentResult<proc_macro2::TokenStream> {
        // Validate return type
        Self::validate_return_type(output)?;

        // Validate parameter order
        Self::validate_parameter_order(params)?;

        // Validate props type (compile-time check)
        Self::validate_props_type_implements_trait(props_type)?;

        // Generate validation code
        let validation_code = Self::generate_props_validation(params, component_name, props_type)?;

        Ok(validation_code)
    }

    /// Extract helpful suggestions for common validation errors
    /// This enhances error messages with actionable guidance
    pub fn get_validation_suggestions(error: &ComponentMacroError) -> Vec<String> {
        match error {
            ComponentMacroError::ReturnType { .. } => vec![
                "Change your function to return Element".to_string(),
                "Use rsx! macro to create Element nodes".to_string(),
                "Example: fn my_component(props: MyProps) -> Element { rsx! { <Text content=\"Hello\" /> } }".to_string(),
            ],
            ComponentMacroError::PropsType { .. } => vec![
                "Add #[derive(Props)] to your props struct".to_string(),
                "Ensure your props struct implements Clone and Debug".to_string(),
                "Example: #[derive(Props, Debug, Clone)] pub struct MyProps { pub title: String }".to_string(),
            ],
            ComponentMacroError::ParameterOrder { .. } => vec![
                "Props parameter must be first".to_string(),
                "Frame parameter should be &mut Frame".to_string(),
                "Area parameter should be Rect".to_string(),
                "Example: fn my_component(props: MyProps, frame: &mut Frame, area: Rect) -> Element".to_string(),
            ],
            ComponentMacroError::FrameParameter { .. } => vec![
                "Frame parameter must be &mut Frame".to_string(),
                "Import Frame from terminus_ui or ratatui".to_string(),
                "Example: fn my_component(props: MyProps, frame: &mut Frame) -> Element".to_string(),
            ],
            ComponentMacroError::AreaParameter { .. } => vec![
                "Area parameter must be Rect".to_string(),
                "Import Rect from terminus_ui or ratatui::layout".to_string(),
                "Example: fn my_component(props: MyProps, area: Rect) -> Element".to_string(),
            ],
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use syn::parse_quote;

    #[test]
    fn test_element_type_detection() {
        let element_type: Type = parse_quote! { Element };
        assert!(ComponentValidator::is_element_type(&element_type));

        let qualified_element: Type = parse_quote! { terminus_ui::Element };
        assert!(ComponentValidator::is_element_type(&qualified_element));

        let not_element: Type = parse_quote! { String };
        assert!(!ComponentValidator::is_element_type(&not_element));
    }

    #[test]
    fn test_return_type_validation() {
        let valid_return: ReturnType = parse_quote! { -> Element };
        assert!(ComponentValidator::validate_return_type(&valid_return).is_ok());

        let invalid_return: ReturnType = parse_quote! { -> String };
        assert!(ComponentValidator::validate_return_type(&invalid_return).is_err());

        let default_return = ReturnType::Default;
        assert!(ComponentValidator::validate_return_type(&default_return).is_err());
    }
}
