use terminus_ui::prelude::*;

/// Demonstration of enhanced callback ergonomics
/// Shows how closures can be used directly without Callback::from() wrapping

#[derive(Props, Debug, Clone)]
pub struct CallbackTestProps {
    pub on_simple: Callback<String>,           // Simple callback
    pub on_with_return: Callback<i32, String>, // Callback with return value
    pub on_validation: Callback<String, bool>, // Validation callback
}

/// Component that demonstrates various callback patterns
#[component(CallbackTest)]
fn callback_test(_props: CallbackTestProps) -> Element {
    let (status, _set_status) = use_state("Ready".to_string());

    rsx! {
        <Block
            title={"Enhanced Callback Demo".to_string()}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Length(1),
                ]}
            >
                <Text content={format!("Status: {}", status.get())} />
                <Text content="Testing different callback patterns:" />
                <Text content="1. Simple callback (no return)" />
                <Text content="2. Callback with return value" />
                <Text content="3. Validation callback (returns bool)" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct EnhancedCallbackDemoAppProps {
    pub title: String,
}

/// Main app demonstrating enhanced callback ergonomics
#[component(EnhancedCallbackDemoApp)]
fn enhanced_callback_demo_app(props: EnhancedCallbackDemoAppProps) -> Element {
    let (messages, set_messages) = use_state(Vec::<String>::new());
    let (_counter, set_counter) = use_state(0);

    // Example 1: Simple callback - no Callback::from() needed!
    let on_simple = {
        let messages = messages.clone();
        let set_messages = set_messages.clone();
        move |message: String| {
            let mut current_messages = messages.get();
            current_messages.push(format!("Simple: {}", message));
            set_messages.set(current_messages);
        }
    };

    // Example 2: Callback with return value - automatic conversion!
    let on_with_return = {
        let set_counter = set_counter.clone();
        move |value: i32| {
            set_counter.set(value);
            format!("Processed value: {}", value * 2)
        }
    };

    // Example 3: Validation callback - returns bool
    let on_validation =
        move |text: String| text.len() >= 3 && text.chars().all(|c| c.is_alphanumeric());

    // Example 4: Using the callback! macro for complex scenarios
    let on_macro_callback = callback!(|_data: String| {
        // println!("Macro callback called with: {}", _data);
    });

    // Example 5: Using callback! macro with return type
    let on_typed_callback = callback!(|x: i32| -> String { format!("Typed result: {}", x * 3) });

    // Test the macro callbacks (these are already Callback types)
    let test_results = {
        on_macro_callback.emit("macro test".to_string());
        let typed_test = on_typed_callback.emit(10);

        // Test validation directly (closure)
        let validation_test = on_validation("valid123".to_string());

        vec![
            format!("Validation result: {}", validation_test),
            format!("Typed macro result: {}", typed_test),
            "Closures work seamlessly as callback props!".to_string(),
        ]
    };

    let message_list = messages.get().join("\n");
    let test_results_text = test_results.join("\n");

    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(1),
                        Constraint::Length(1),
                        Constraint::Length(1),
                    ]}
                >
                    <Text content="Enhanced Callback System Demonstration" />
                    <Text content="✨ Closures automatically convert to Callbacks!" />
                    <Text content="🚀 No more verbose Callback::from() wrapping needed" />
                </Layout>
            </Block>

            // Main content
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),  // Left: Component
                    Constraint::Percentage(50),  // Right: Results
                ]}
            >
                // Test component
                <CallbackTest
                    on_simple={on_simple}
                    on_with_return={on_with_return}
                    on_validation={on_validation}
                />

                // Results display
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Percentage(50),  // Messages
                        Constraint::Percentage(50),  // Test results
                    ]}
                >
                    <Block
                        title={"Callback Messages".to_string()}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Green)}
                    >
                        <Text content={if message_list.is_empty() {
                            "No messages yet".to_string()
                        } else {
                            message_list
                        }} />
                    </Block>

                    <Block
                        title={"Test Results".to_string()}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Yellow)}
                    >
                        <Text content={test_results_text} />
                    </Block>
                </Layout>
            </Layout>

            // Code examples
            <Block
                title={"Code Examples".to_string()}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(1),
                        Constraint::Length(1),
                        Constraint::Length(1),
                        Constraint::Length(1),
                    ]}
                >
                    <Text content="// Before: Callback::from(move |x| { ... })" />
                    <Text content="// After:  move |x| { ... }  ← Automatic conversion!" />
                    <Text content="// Macro:  callback!(|x: i32| x * 2)" />
                    <Text content="// Typed:  callback!(|x: i32| -> String { format!(\"{}\", x) })" />
                </Layout>
            </Block>

            // Footer
            <Block
                title={"Benefits".to_string()}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::White)}
            >
                <Text content="✅ Less verbose • ✅ Better ergonomics • ✅ Type-safe • ✅ All existing functionality preserved • ✅ Works with RSX macro" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_props = EnhancedCallbackDemoAppProps {
        title: "🚀 Enhanced Callback System Demo".to_string(),
    };

    let element = rsx! {
        <EnhancedCallbackDemoApp title={app_props.title} />
    };

    render(element)
}
