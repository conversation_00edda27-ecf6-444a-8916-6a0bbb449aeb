//! Comprehensive tests for the useFuture hook
//!
//! This module contains extensive tests covering:
//! - Basic future execution and state management
//! - Dependency tracking and re-execution
//! - Error handling and edge cases
//! - Cancellation behavior
//! - Thread safety and concurrent access

use crate::{
    FutureState,
    hooks::{HookContext, clear_hook_context, set_hook_context},
    use_future,
};
use std::rc::Rc;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::time::sleep;

// Helper function to simulate a component render with hook context
fn with_component_context<F, R>(f: F) -> R
where
    F: FnOnce() -> R,
{
    let context = Rc::new(HookContext::new());
    set_hook_context(context);

    let result = f();

    clear_hook_context();
    result
}

#[tokio::test]
async fn test_basic_future_success() {
    let handle = with_component_context(|| use_future(|| async { Ok::<i32, String>(42) }, ()));

    // Initially should be pending
    assert!(handle.is_pending());
    assert_eq!(handle.state(), FutureState::Pending);

    // Wait for the future to complete
    sleep(Duration::from_millis(10)).await;

    // Should now be resolved
    assert!(handle.is_resolved());
    assert_eq!(handle.value(), Some(42));
    assert_eq!(handle.state(), FutureState::Resolved(42));
}

#[tokio::test]
async fn test_basic_future_error() {
    let handle = with_component_context(|| {
        use_future(
            || async { Err::<i32, String>("Test error".to_string()) },
            (),
        )
    });

    // Initially should be pending
    assert!(handle.is_pending());

    // Wait for the future to complete
    sleep(Duration::from_millis(10)).await;

    // Should now be in error state
    assert!(handle.is_error());
    assert_eq!(handle.error(), Some("Test error".to_string()));
    assert_eq!(handle.state(), FutureState::Error("Test error".to_string()));
}

#[tokio::test]
async fn test_future_with_dependencies() {
    let counter = Arc::new(AtomicUsize::new(0));
    let context = Rc::new(HookContext::new());
    set_hook_context(context.clone());

    // First render
    let counter_clone = counter.clone();
    let handle1 = use_future(
        move || {
            let counter = counter_clone.clone();
            async move {
                counter.fetch_add(1, Ordering::SeqCst);
                Ok::<usize, String>(42)
            }
        },
        1, // dependency value
    );

    // Wait for the future to complete
    sleep(Duration::from_millis(10)).await;
    assert_eq!(counter.load(Ordering::SeqCst), 1);
    assert!(handle1.is_resolved());

    // Second render with same dependency - should not re-execute
    context.reset(); // Reset hook index for next render
    let counter_clone2 = counter.clone();
    let _handle2 = use_future(
        move || {
            let counter = counter_clone2.clone();
            async move {
                counter.fetch_add(1, Ordering::SeqCst);
                Ok::<usize, String>(42)
            }
        },
        1, // same dependency value
    );

    sleep(Duration::from_millis(10)).await;

    // Should still have executed only once since dependency didn't change
    assert_eq!(counter.load(Ordering::SeqCst), 1);

    clear_hook_context();
}

#[tokio::test]
async fn test_future_dependency_change() {
    let counter = Arc::new(AtomicUsize::new(0));
    let results = Arc::new(Mutex::new(Vec::new()));

    // First execution with dependency = 1
    {
        let counter_clone = counter.clone();
        let results_clone = results.clone();
        let handle = with_component_context(|| {
            use_future(
                move || {
                    let counter = counter_clone.clone();
                    let results = results_clone.clone();
                    async move {
                        let count = counter.fetch_add(1, Ordering::SeqCst);
                        let result = format!("execution_{}", count);
                        results.lock().unwrap().push(result.clone());
                        Ok::<String, String>(result)
                    }
                },
                1, // dependency = 1
            )
        });

        sleep(Duration::from_millis(10)).await;
        assert!(handle.is_resolved());
    }

    // Second execution with dependency = 2 (changed)
    {
        let counter_clone = counter.clone();
        let results_clone = results.clone();
        let handle = with_component_context(|| {
            use_future(
                move || {
                    let counter = counter_clone.clone();
                    let results = results_clone.clone();
                    async move {
                        let count = counter.fetch_add(1, Ordering::SeqCst);
                        let result = format!("execution_{}", count);
                        results.lock().unwrap().push(result.clone());
                        Ok::<String, String>(result)
                    }
                },
                2, // dependency = 2 (changed)
            )
        });

        sleep(Duration::from_millis(10)).await;
        assert!(handle.is_resolved());
    }

    // Should have executed twice due to dependency change
    assert_eq!(counter.load(Ordering::SeqCst), 2);
    let results_vec = results.lock().unwrap();
    assert_eq!(results_vec.len(), 2);
    assert_eq!(results_vec[0], "execution_0");
    assert_eq!(results_vec[1], "execution_1");
}

#[tokio::test]
async fn test_future_state_methods() {
    // Test FutureState utility methods
    let pending: FutureState<i32, String> = FutureState::Pending;
    assert!(pending.is_pending());
    assert!(!pending.is_resolved());
    assert!(!pending.is_error());
    assert_eq!(pending.value(), None);
    assert_eq!(pending.error(), None);

    let resolved: FutureState<i32, String> = FutureState::Resolved(42);
    assert!(!resolved.is_pending());
    assert!(resolved.is_resolved());
    assert!(!resolved.is_error());
    assert_eq!(resolved.value(), Some(&42));
    assert_eq!(resolved.error(), None);

    let error: FutureState<i32, String> = FutureState::Error("test error".to_string());
    assert!(!error.is_pending());
    assert!(!error.is_resolved());
    assert!(error.is_error());
    assert_eq!(error.value(), None);
    assert_eq!(error.error(), Some(&"test error".to_string()));
}

#[tokio::test]
async fn test_future_state_map() {
    let resolved: FutureState<i32, String> = FutureState::Resolved(42);
    let mapped = resolved.map(|x| x * 2);
    assert_eq!(mapped, FutureState::Resolved(84));

    let pending: FutureState<i32, String> = FutureState::Pending;
    let mapped_pending = pending.map(|x| x * 2);
    assert_eq!(mapped_pending, FutureState::Pending);

    let error: FutureState<i32, String> = FutureState::Error("test".to_string());
    let mapped_error = error.map(|x: i32| x * 2);
    assert_eq!(mapped_error, FutureState::Error("test".to_string()));
}

#[tokio::test]
async fn test_future_state_map_err() {
    let error: FutureState<i32, String> = FutureState::Error("test".to_string());
    let mapped = error.map_err(|e| format!("Error: {}", e));
    assert_eq!(mapped, FutureState::Error("Error: test".to_string()));

    let resolved: FutureState<i32, String> = FutureState::Resolved(42);
    let mapped_resolved = resolved.map_err(|e: String| format!("Error: {}", e));
    assert_eq!(mapped_resolved, FutureState::Resolved(42));
}

#[tokio::test]
async fn test_future_handle_clone() {
    let handle = with_component_context(|| use_future(|| async { Ok::<i32, String>(42) }, ()));

    let handle_clone = handle.clone();

    // Wait for completion
    sleep(Duration::from_millis(10)).await;

    // Both handles should see the same state
    assert_eq!(handle.state(), handle_clone.state());
    assert_eq!(handle.value(), handle_clone.value());
    assert!(handle.is_resolved());
    assert!(handle_clone.is_resolved());
}
