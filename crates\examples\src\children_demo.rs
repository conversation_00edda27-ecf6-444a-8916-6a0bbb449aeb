use terminus_ui::prelude::*;

// Define props for a Container component that accepts children
#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ContainerProps {
    pub title: String,
    #[children]
    pub children: Children,
}

// Define props for a Card component that accepts children
#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>lone)]
pub struct CardProps {
    pub variant: String,
    pub children: Children,
}

// Define a simple component without children
#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON><PERSON>)]
pub struct HeaderProps {
    pub text: String,
}

// Container component that renders its children
#[component(Container)]
fn container(props: ContainerProps) -> Element {
    rsx! {
        <Block title={props.title}>
            {props.children.clone()}
        </Block>
    }
}

// Card component that renders its children
#[component(Card)]
fn card(props: CardProps) -> Element {
    rsx! {
        <Block title={format!("Card - {}", props.variant)}>
            {props.children.clone()}
        </Block>
    }
}

// Header component without children
#[component(Header)]
fn header(props: HeaderProps) -> Element {
    rsx! {
        <Text content={props.text} />
    }
}

// Props for the ChildrenDemo component
#[derive(<PERSON><PERSON>, Debu<PERSON>, <PERSON>lone)]
pub struct ChildrenDemoProps {
    pub demo_title: Option<String>,
    #[children]
    pub children: Children,
}

// Demo component showing children usage with automatic detection
#[component(ChildrenDemo)]
fn children_demo(props: ChildrenDemoProps) -> Element {
    let title = props
        .demo_title
        .unwrap_or_else(|| "Main Container".to_string());

    // Test automatic children detection with RSX syntax
    // If children are provided, use them; otherwise use default content
    if props.children.is_empty() {
        rsx! {
            <Container title={title}>
                <Header text="Welcome to the Children Demo!" />
                <Card variant="primary">
                    <Text content="This is inside a primary card" />
                    <Text content="Multiple children are supported" />
                </Card>
                <Card variant="secondary">
                    <Header text="Nested components work too!" />
                    <Text content="This demonstrates nested children" />
                </Card>
            </Container>
        }
    } else {
        rsx! {
            <Container title={title}>
                {props.children.clone()}
            </Container>
        }
    }
}

// Demo function showing manual children usage (for comparison)
pub fn children_demo_manual() -> Element {
    // Create children manually for comparison
    let header_element = Header::create_typed_element(HeaderProps {
        text: "Welcome to the Children Demo!".to_string(),
    });

    let card1_children = Children::from_vec(vec![
        rsx! { <Text content="This is inside a primary card" /> },
        rsx! { <Text content="Multiple children are supported" /> },
    ]);

    let card1_element = Card::create_typed_element(CardProps {
        variant: "primary".to_string(),
        children: card1_children,
    });

    let card2_children = Children::from_vec(vec![
        Header::create_typed_element(HeaderProps {
            text: "Nested components work too!".to_string(),
        }),
        rsx! { <Text content="This demonstrates nested children" /> },
    ]);

    let card2_element = Card::create_typed_element(CardProps {
        variant: "secondary".to_string(),
        children: card2_children,
    });

    let container_children = Children::from_vec(vec![header_element, card1_element, card2_element]);

    Container::create_typed_element(ContainerProps {
        title: "Main Container".to_string(),
        children: container_children,
    })
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = ChildrenDemo::create_typed_element(ChildrenDemoProps {
        demo_title: Some("Main Container".to_string()),
        children: Children::new(), // Use default children
    });
    render(element)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_children_demo_compiles() {
        let _element = ChildrenDemo::create_typed_element(ChildrenDemoProps {
            demo_title: Some("Test Container".to_string()),
            children: Children::new(),
        });
        // If this compiles, the children system is working
    }

    #[test]
    fn test_container_with_children() {
        let children = Children::from_vec(vec![
            rsx! { <Text content="Child 1" /> },
            rsx! { <Text content="Child 2" /> },
        ]);

        let props = ContainerProps {
            title: "Test Container".to_string(),
            children,
        };

        let _element = Container::create_typed_element(props);
        // If this compiles, the children system is working
    }

    #[test]
    fn test_empty_children() {
        let props = ContainerProps {
            title: "Empty Container".to_string(),
            children: Children::new(),
        };

        let _element = Container::create_typed_element(props);
        // If this compiles, empty children work
    }

    #[test]
    fn test_automatic_children_detection() {
        // Test that the RSX macro automatically detects and handles children
        let _element = rsx! {
            <Container title="Auto Container">
                <Header text="Auto Header" />
                <Card variant="auto">
                    <Text content="Auto content" />
                </Card>
            </Container>
        };
        // If this compiles, automatic children detection is working

        // Also test the ChildrenDemo component with RSX syntax
        let _demo_element = rsx! {
            <ChildrenDemo demo_title={Some("RSX Test Container".to_string())}>
                <Header text="Custom child in ChildrenDemo" />
                <Text content="This is a custom child element" />
            </ChildrenDemo>
        };
    }

    #[test]
    fn test_try_set_children_trait() {
        // Test the TrySetChildren trait implementation
        let mut container_props = ContainerProps {
            title: "Test".to_string(),
            children: Children::new(),
        };

        let test_children = Children::from_vec(vec![rsx! { <Text content="Test child" /> }]);

        // This should return true since ContainerProps implements HasChildren
        let result = container_props.try_set_children(test_children);
        assert!(
            result,
            "TrySetChildren should return true for props with children support"
        );

        // Test with props that don't have children
        let mut header_props = HeaderProps {
            text: "Test".to_string(),
        };

        let test_children2 = Children::from_vec(vec![rsx! { <Text content="Test child 2" /> }]);

        // This should return false since HeaderProps doesn't implement HasChildren
        let result2 = header_props.try_set_children(test_children2);
        assert!(
            !result2,
            "TrySetChildren should return false for props without children support"
        );
    }
}
