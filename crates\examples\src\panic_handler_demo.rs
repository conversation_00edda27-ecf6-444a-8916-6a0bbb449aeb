use terminus_ui::{setup_panic_handler, spawn_catch_panic};
use tokio::time::{self, Duration};

#[tokio::main]
async fn main() {
    // Initialize the panic handler
    // This sets up the panic handler to log panics and handle them gracefully
    // It should be called before any async tasks are spawned to ensure proper handling
    // of panics in those tasks.
    // This function should only be called once, so it uses a static initializer.
    // It configures the panic behavior based on the build profile:
    // - **Debug builds**: Uses `better_panic` for verbose, immediate, and diagnostic-rich panics with full stack traces.
    // - **Release builds**: Uses `human_panic` for graceful, user-friendly panics that log internally without exposing sensitive details, prioritizing user experience.
    // Additionally, it provides a mechanism to catch panics from spawned Tokio tasks.
    // This function should be called only once. Subsequent calls will be ignored.
    // This is typically done at the start of the application to ensure all panics are handled.
    setup_panic_handler();

    println!("Application starting...");

    // Simulate an async task that panics
    let handle = spawn_catch_panic(async {
        println!("Async task started...");
        time::sleep(Duration::from_secs(1)).await;
        panic!("Panic from an asynchronous task!");
    });

    // Simulate another async task that completes successfully
    let handle2 = spawn_catch_panic(async {
        println!("Another async task started...");
        time::sleep(Duration::from_secs(2)).await;
        println!("Another async task completed successfully.");
    });

    // Wait for the tasks to complete (or panic)
    let _ = tokio::join!(handle, handle2);

    println!("Application finished (if no panic occurred).");
}
