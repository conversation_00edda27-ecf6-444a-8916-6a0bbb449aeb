//! Scroll-related types and components for terminus-ui
//!
//! This module provides scrolling functionality including ScrollArea and ScrollBar components
//! with support for both horizontal and vertical scrolling, customizable appearance,
//! and smooth user interactions.

use crate::{ComponentProps, PropFieldMetadata};
use ratatui::style::Style;

/// Orientation for scrolling components
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScrollOrientation {
    /// Vertical scrolling (up/down)
    Vertical,
    /// Horizontal scrolling (left/right)
    Horizontal,
}

impl Default for ScrollOrientation {
    fn default() -> Self {
        Self::Vertical
    }
}

/// Position of the scrollbar relative to the content area
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ScrollbarPosition {
    /// Top edge (for horizontal scrollbars)
    Top,
    /// Bottom edge (for horizontal scrollbars)
    Bottom,
    /// Left edge (for vertical scrollbars)
    Left,
    /// Right edge (for vertical scrollbars)
    Right,
}

impl Default for ScrollbarPosition {
    fn default() -> Self {
        Self::Right
    }
}

/// Scrollbar appearance configuration
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ScrollbarAppearance {
    /// Symbol for the beginning of the scrollbar (e.g., "↑" for vertical, "←" for horizontal)
    pub begin_symbol: String,
    /// Symbol for the end of the scrollbar (e.g., "↓" for vertical, "→" for horizontal)
    pub end_symbol: String,
    /// Symbol for the scrollbar track/background
    pub track_symbol: String,
    /// Symbol for the scrollbar thumb/handle
    pub thumb_symbol: String,
    /// Style for the scrollbar track
    pub track_style: Option<Style>,
    /// Style for the scrollbar thumb
    pub thumb_style: Option<Style>,
    /// Style for the begin/end symbols
    pub symbol_style: Option<Style>,
}

impl Default for ScrollbarAppearance {
    fn default() -> Self {
        Self {
            begin_symbol: "▲".to_string(),
            end_symbol: "▼".to_string(),
            track_symbol: "│".to_string(),
            thumb_symbol: "█".to_string(),
            track_style: None,
            thumb_style: None,
            symbol_style: None,
        }
    }
}

impl ScrollbarAppearance {
    /// Create horizontal scrollbar appearance with appropriate symbols
    pub fn horizontal() -> Self {
        Self {
            begin_symbol: "◀".to_string(),
            end_symbol: "▶".to_string(),
            track_symbol: "─".to_string(),
            thumb_symbol: "█".to_string(),
            track_style: None,
            thumb_style: None,
            symbol_style: None,
        }
    }

    /// Create vertical scrollbar appearance with appropriate symbols
    pub fn vertical() -> Self {
        Self::default()
    }
}

/// Scroll state information
#[derive(Debug, Clone, Copy, PartialEq)]
pub struct ScrollState {
    /// Current scroll offset (0-based)
    pub offset: usize,
    /// Total content size
    pub content_size: usize,
    /// Visible viewport size
    pub viewport_size: usize,
}

impl Default for ScrollState {
    fn default() -> Self {
        Self {
            offset: 0,
            content_size: 0,
            viewport_size: 0,
        }
    }
}

impl ScrollState {
    /// Create a new scroll state
    pub fn new(content_size: usize, viewport_size: usize) -> Self {
        Self {
            offset: 0,
            content_size,
            viewport_size,
        }
    }

    /// Check if scrolling is needed (content is larger than viewport)
    pub fn needs_scrolling(&self) -> bool {
        self.content_size > self.viewport_size
    }

    /// Get the maximum scroll offset
    pub fn max_offset(&self) -> usize {
        if self.content_size > self.viewport_size {
            self.content_size - self.viewport_size
        } else {
            0
        }
    }

    /// Scroll to a specific offset, clamping to valid range
    pub fn scroll_to(&mut self, offset: usize) {
        self.offset = offset.min(self.max_offset());
    }

    /// Scroll by a relative amount (positive = forward, negative = backward)
    pub fn scroll_by(&mut self, delta: isize) {
        let new_offset = if delta < 0 {
            self.offset.saturating_sub((-delta) as usize)
        } else {
            self.offset.saturating_add(delta as usize)
        };
        self.scroll_to(new_offset);
    }

    /// Scroll to the beginning
    pub fn scroll_to_start(&mut self) {
        self.offset = 0;
    }

    /// Scroll to the end
    pub fn scroll_to_end(&mut self) {
        self.offset = self.max_offset();
    }

    /// Get the scroll progress as a ratio (0.0 to 1.0)
    pub fn scroll_progress(&self) -> f64 {
        if self.max_offset() == 0 {
            0.0
        } else {
            self.offset as f64 / self.max_offset() as f64
        }
    }

    /// Calculate thumb position and size for scrollbar rendering
    pub fn thumb_info(&self, scrollbar_size: usize) -> (usize, usize) {
        if !self.needs_scrolling() || scrollbar_size <= 2 {
            return (0, scrollbar_size);
        }

        // Reserve space for begin/end symbols
        let available_space = scrollbar_size.saturating_sub(2);
        if available_space == 0 {
            return (0, 0);
        }

        // Calculate thumb size based on viewport ratio
        let thumb_size = ((self.viewport_size as f64 / self.content_size as f64)
            * available_space as f64)
            .max(1.0) as usize;

        // Calculate thumb position
        let max_thumb_pos = available_space.saturating_sub(thumb_size);
        let thumb_pos = if self.max_offset() == 0 {
            0
        } else {
            ((self.offset as f64 / self.max_offset() as f64) * max_thumb_pos as f64) as usize
        };

        (thumb_pos + 1, thumb_size) // +1 to account for begin symbol
    }
}

/// Props for the ScrollArea component
#[derive(Debug, Clone)]
pub struct ScrollAreaProps {
    /// Content to be scrolled
    pub children: crate::Children,
    /// Horizontal scroll state
    pub horizontal_scroll: Option<ScrollState>,
    /// Vertical scroll state  
    pub vertical_scroll: Option<ScrollState>,
    /// Whether to show horizontal scrollbar
    pub show_horizontal_scrollbar: bool,
    /// Whether to show vertical scrollbar
    pub show_vertical_scrollbar: bool,
    /// Position of horizontal scrollbar
    pub horizontal_scrollbar_position: ScrollbarPosition,
    /// Position of vertical scrollbar
    pub vertical_scrollbar_position: ScrollbarPosition,
    /// Appearance configuration for horizontal scrollbar
    pub horizontal_scrollbar_appearance: ScrollbarAppearance,
    /// Appearance configuration for vertical scrollbar
    pub vertical_scrollbar_appearance: ScrollbarAppearance,
    /// Style for the content area
    pub content_style: Option<Style>,
}

impl Default for ScrollAreaProps {
    fn default() -> Self {
        Self {
            children: crate::Children::default(),
            horizontal_scroll: None,
            vertical_scroll: None,
            show_horizontal_scrollbar: true,
            show_vertical_scrollbar: true,
            horizontal_scrollbar_position: ScrollbarPosition::Bottom,
            vertical_scrollbar_position: ScrollbarPosition::Right,
            horizontal_scrollbar_appearance: ScrollbarAppearance::horizontal(),
            vertical_scrollbar_appearance: ScrollbarAppearance::vertical(),
            content_style: None,
        }
    }
}

impl ComponentProps for ScrollAreaProps {
    fn validate(&self) -> Result<(), String> {
        // Validate scrollbar positions make sense for their orientations
        match self.horizontal_scrollbar_position {
            ScrollbarPosition::Top | ScrollbarPosition::Bottom => {}
            _ => return Err("Horizontal scrollbar position must be Top or Bottom".to_string()),
        }

        match self.vertical_scrollbar_position {
            ScrollbarPosition::Left | ScrollbarPosition::Right => {}
            _ => return Err("Vertical scrollbar position must be Left or Right".to_string()),
        }

        Ok(())
    }

    fn field_metadata() -> Vec<PropFieldMetadata> {
        vec![
            PropFieldMetadata {
                name: "children".to_string(),
                type_name: "Children".to_string(),
                is_optional: false,
                default_value: Some("Children::default()".to_string()),
            },
            PropFieldMetadata {
                name: "horizontal_scroll".to_string(),
                type_name: "Option<ScrollState>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            PropFieldMetadata {
                name: "vertical_scroll".to_string(),
                type_name: "Option<ScrollState>".to_string(),
                is_optional: true,
                default_value: Some("None".to_string()),
            },
            PropFieldMetadata {
                name: "show_horizontal_scrollbar".to_string(),
                type_name: "bool".to_string(),
                is_optional: false,
                default_value: Some("true".to_string()),
            },
            PropFieldMetadata {
                name: "show_vertical_scrollbar".to_string(),
                type_name: "bool".to_string(),
                is_optional: false,
                default_value: Some("true".to_string()),
            },
        ]
    }
}

/// Props for the ScrollBar component
#[derive(Debug, Clone)]
pub struct ScrollBarProps {
    /// Scroll orientation
    pub orientation: ScrollOrientation,
    /// Scroll state
    pub scroll_state: ScrollState,
    /// Scrollbar position
    pub position: ScrollbarPosition,
    /// Appearance configuration
    pub appearance: ScrollbarAppearance,
    /// Size of the scrollbar (width for vertical, height for horizontal)
    pub size: Option<u16>,
}

impl Default for ScrollBarProps {
    fn default() -> Self {
        Self {
            orientation: ScrollOrientation::Vertical,
            scroll_state: ScrollState::default(),
            position: ScrollbarPosition::Right,
            appearance: ScrollbarAppearance::default(),
            size: None,
        }
    }
}

impl ComponentProps for ScrollBarProps {
    fn validate(&self) -> Result<(), String> {
        // Validate position matches orientation
        match (self.orientation, self.position) {
            (ScrollOrientation::Horizontal, ScrollbarPosition::Top | ScrollbarPosition::Bottom) => {
            }
            (ScrollOrientation::Vertical, ScrollbarPosition::Left | ScrollbarPosition::Right) => {}
            _ => return Err("Scrollbar position must match orientation".to_string()),
        }

        Ok(())
    }

    fn field_metadata() -> Vec<PropFieldMetadata> {
        vec![
            PropFieldMetadata {
                name: "orientation".to_string(),
                type_name: "ScrollOrientation".to_string(),
                is_optional: false,
                default_value: Some("ScrollOrientation::Vertical".to_string()),
            },
            PropFieldMetadata {
                name: "scroll_state".to_string(),
                type_name: "ScrollState".to_string(),
                is_optional: false,
                default_value: Some("ScrollState::default()".to_string()),
            },
            PropFieldMetadata {
                name: "position".to_string(),
                type_name: "ScrollbarPosition".to_string(),
                is_optional: false,
                default_value: Some("ScrollbarPosition::Right".to_string()),
            },
        ]
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scroll_state_creation() {
        let state = ScrollState::new(100, 20);
        assert_eq!(state.content_size, 100);
        assert_eq!(state.viewport_size, 20);
        assert_eq!(state.offset, 0);
    }

    #[test]
    fn test_scroll_state_needs_scrolling() {
        let state_no_scroll = ScrollState::new(10, 20);
        assert!(!state_no_scroll.needs_scrolling());

        let state_needs_scroll = ScrollState::new(100, 20);
        assert!(state_needs_scroll.needs_scrolling());
    }

    #[test]
    fn test_scroll_state_max_offset() {
        let state = ScrollState::new(100, 20);
        assert_eq!(state.max_offset(), 80);

        let state_no_scroll = ScrollState::new(10, 20);
        assert_eq!(state_no_scroll.max_offset(), 0);
    }

    #[test]
    fn test_scroll_state_scroll_to() {
        let mut state = ScrollState::new(100, 20);

        // Normal scroll
        state.scroll_to(50);
        assert_eq!(state.offset, 50);

        // Clamp to max
        state.scroll_to(200);
        assert_eq!(state.offset, 80);

        // Clamp to min
        state.scroll_to(0);
        assert_eq!(state.offset, 0);
    }

    #[test]
    fn test_scroll_state_scroll_by() {
        let mut state = ScrollState::new(100, 20);

        // Scroll down
        state.scroll_by(10);
        assert_eq!(state.offset, 10);

        // Scroll up
        state.scroll_by(-5);
        assert_eq!(state.offset, 5);

        // Clamp at boundaries
        state.scroll_by(-20);
        assert_eq!(state.offset, 0);

        state.scroll_by(200);
        assert_eq!(state.offset, 80);
    }

    #[test]
    fn test_scroll_state_thumb_info() {
        let state = ScrollState::new(100, 20);
        let (thumb_pos, thumb_size) = state.thumb_info(10);

        // Thumb size should be proportional to viewport/content ratio
        assert!(thumb_size > 0);
        assert!(thumb_size <= 10);
        assert!(thumb_pos <= 10 - thumb_size);
    }

    #[test]
    fn test_scrollbar_appearance_defaults() {
        let vertical = ScrollbarAppearance::vertical();
        assert_eq!(vertical.begin_symbol, "▲");
        assert_eq!(vertical.end_symbol, "▼");

        let horizontal = ScrollbarAppearance::horizontal();
        assert_eq!(horizontal.begin_symbol, "◀");
        assert_eq!(horizontal.end_symbol, "▶");
    }
}
