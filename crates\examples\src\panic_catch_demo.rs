use terminus_ui::{panic_handler::catch_panic, setup_panic_handler};

fn main() {
    setup_panic_handler();

    // Example: successful execution
    let result_ok = catch_panic(|| {
        println!("This will succeed.");
        123
    });
    match result_ok {
        Ok(val) => println!("Success: {}", val),
        Err(_) => println!("Unexpected panic!"),
    }

    // Example: panicking execution
    let result_panic = catch_panic(|| {
        println!("About to panic!");
        panic!("Intentional panic for demonstration.");
    });
    match result_panic {
        Ok(_) => println!("Unexpected success!"),
        Err(payload) => {
            if let Some(s) = payload.downcast_ref::<&str>() {
                println!("Caught panic with payload: {}", s);
            } else if let Some(s) = payload.downcast_ref::<String>() {
                println!("Caught panic with payload: {}", s);
            } else {
                println!("Caught panic with unknown payload type.");
            }
        }
    }
}
