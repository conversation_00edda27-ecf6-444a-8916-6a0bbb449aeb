/// Simple ScrollArea example
/// This demonstrates basic scrollable content usage
use terminus_ui::prelude::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <Block title="Simple ScrollArea Example" borders={Borders::ALL}>
            <ScrollArea>
                <Text content="Line 1: Welcome to the ScrollArea component!" />
                <Text content="Line 2: This content is scrollable using arrow keys." />
                <Text content="Line 3: The scrollbar appears automatically when needed." />
                <Text content="Line 4: Use Page Up/Down for faster scrolling." />
                <Text content="Line 5: Home/End keys jump to start/end of content." />
                <Text content="Line 6: Mouse wheel scrolling is also supported." />
                <Text content="Line 7: The component follows React-like patterns." />
                <Text content="Line 8: It integrates seamlessly with other components." />
                <Text content="Line 9: Performance is optimized for smooth scrolling." />
                <Text content="Line 10: This is the last line of content." />
            </ScrollArea>
        </Block>
    };

    render(element)
}
