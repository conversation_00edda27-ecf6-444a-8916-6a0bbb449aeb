use terminus_ui::prelude::*;

// Define props for a UserCard component
#[derive(<PERSON><PERSON>, <PERSON>bug, <PERSON>lone)]
pub struct UserCardProps {
    pub name: String,
    pub age: u32,
    pub role: String,
    pub is_online: bool,
}

// Define props for a StatusBadge component
#[derive(<PERSON><PERSON>, Debug, Clone)]
pub struct StatusBadgeProps {
    pub is_online: bool,
    pub label: String,
}

// Define props for a Dashboard component
#[derive(<PERSON><PERSON>, Debu<PERSON>, <PERSON>lone)]
pub struct DashboardProps {
    pub title: String,
    pub user_count: u32,
}

// StatusBadge component - shows online/offline status
#[component(StatusBadge)]
fn status_badge(props: StatusBadgeProps) -> Element {
    let (color, status_text) = if props.is_online {
        (Color::Green, "●")
    } else {
        (Color::Red, "○")
    };

    rsx! {
        <Block borders={Borders::ALL} border_style={Style::default().fg(color)}>
            <Text content={format!("{} {}", status_text, props.label)} />
        </Block>
    }
}

// UserCard component - displays user information with status
#[component(UserCard)]
fn user_card(props: UserCardProps) -> Element {
    let border_color = if props.is_online {
        Color::Green
    } else {
        Color::Gray
    };

    let status_text = if props.is_online {
        "● Online"
    } else {
        "○ Offline"
    };

    rsx! {
        <Block
            title={props.name.clone()}
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),  // Age
                    Constraint::Length(1),  // Role
                    Constraint::Length(3),  // Status (give more space)
                ]}
            >
                <Text content={format!("Age: {}", props.age)} />
                <Text content={format!("Role: {}", props.role)} />
                <Block
                    title="Status"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(if props.is_online { Color::Green } else { Color::Red })}
                >
                    <Text content={status_text} />
                </Block>
            </Layout>
        </Block>
    }
}

// Dashboard component - main container with multiple user cards
#[component(Dashboard)]
fn dashboard(props: DashboardProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content={format!("Total Users: {}", props.user_count)} />
            </Block>

            // User cards in horizontal layout
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(33),
                    Constraint::Percentage(33),
                    Constraint::Percentage(34),
                ]}
            >
                <UserCard
                    name={"Alice Johnson".to_string()}
                    age={28}
                    role={"Developer".to_string()}
                    is_online={true}
                />

                <UserCard
                    name={"Bob Smith".to_string()}
                    age={35}
                    role={"Designer".to_string()}
                    is_online={false}
                />

                <UserCard
                    name={"Carol Davis".to_string()}
                    age={42}
                    role={"Manager".to_string()}
                    is_online={true}
                />
            </Layout>

            // Footer
            <Block
                title="Controls"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="Press 'q' to quit | Press 'r' to refresh" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let dashboard_props = DashboardProps {
        title: "User Management Dashboard".to_string(),
        user_count: 3,
    };

    let element = rsx! {
        <Dashboard
            title={dashboard_props.title}
            user_count={dashboard_props.user_count}
        />
    };

    render(element)
}
