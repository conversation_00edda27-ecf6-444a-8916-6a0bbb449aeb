[package]
name = "terminus_ui_rsx_macro"
version = "0.1.0"
edition = "2024"

[lib]
proc-macro = true

[dependencies]
convert_case = { workspace = true }
proc-macro2 = { workspace = true, features = ["span-locations"] }
proc-macro2-diagnostics = { workspace = true }
quote = { workspace = true }
ratatui = { workspace = true }
syn = { workspace = true, features = [
    "full",
    "extra-traits",
    "visit",
    "visit-mut",
] }
