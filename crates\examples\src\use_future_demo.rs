use std::time::Duration;
use terminus_ui::prelude::*;
use tokio::time::sleep;

// Simulate an API response
#[derive(Debu<PERSON>, <PERSON>lone)]
struct User {
    id: u32,
    name: String,
    email: String,
}

// Simulate async API calls
async fn fetch_user_data(user_id: u32) -> Result<User, String> {
    // Simulate network delay
    sleep(Duration::from_millis(800)).await;

    // Simulate occasional errors
    if user_id == 999 {
        return Err("User not found".to_string());
    }

    Ok(User {
        id: user_id,
        name: format!("User {}", user_id),
        email: format!("user{}@example.com", user_id),
    })
}

async fn fetch_weather_data(city: String) -> Result<String, String> {
    sleep(Duration::from_millis(600)).await;

    if city == "Unknown" {
        return Err("City not found".to_string());
    }

    Ok(format!("Sunny, 22°C in {}", city))
}

// Component demonstrating basic use_future usage
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
struct UserProfileProps {
    user_id: u32,
}

#[component(UserProfile)]
fn user_profile(props: UserProfileProps) -> Element {
    // Fetch user data when user_id changes
    let user_future = use_future(move || fetch_user_data(props.user_id), props.user_id);

    rsx! {
        <Block
            title="👤 User Profile"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            {match user_future.state() {
                FutureState::Pending => rsx! {
                    <Text content="🔄 Loading user data..." />
                },
                FutureState::Resolved(user) => rsx! {
                    <Layout direction={Direction::Vertical}>
                        <Text content={format!("📋 ID: {}", user.id)} />
                        <Text content={format!("👤 Name: {}", user.name)} />
                        <Text content={format!("📧 Email: {}", user.email)} />
                    </Layout>
                },
                FutureState::Error(err) => rsx! {
                    <Text content={format!("❌ Error: {}", err)} />
                },
            }}
        </Block>
    }
}

// Component demonstrating manual triggering
#[derive(Props, Debug, Clone)]
struct WeatherWidgetProps {}

#[component(WeatherWidget)]
fn weather_widget(_props: WeatherWidgetProps) -> Element {
    let (city, _set_city) = use_state("London".to_string());
    let city_value = city.get();

    // Fetch weather data for the current city
    let weather_future = use_future(
        {
            let city_clone = city_value.clone();
            move || fetch_weather_data(city_clone)
        },
        (), // Run once on mount
    );

    rsx! {
        <Block
            title="🌤️ Weather Widget"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content="🌍 Available cities: London, Paris, Tokyo, Unknown" />
                <Text content="🔄 Weather refreshes automatically every few seconds" />
                <Text content={format!("📍 Current city: {}", city_value)} />

                {match weather_future.state() {
                    FutureState::Pending => rsx! {
                        <Text content="🔄 Loading weather data..." />
                    },
                    FutureState::Resolved(weather) => rsx! {
                        <Text content={format!("🌤️ {}", weather)} />
                    },
                    FutureState::Error(err) => rsx! {
                        <Text content={format!("❌ Weather error: {}", err)} />
                    },
                }}
            </Layout>
        </Block>
    }
}

// Component demonstrating multiple concurrent futures
#[derive(Props, Debug, Clone)]
struct DataDashboardProps {}

#[component(DataDashboard)]
fn data_dashboard(_props: DataDashboardProps) -> Element {
    let (selected_user, _set_selected_user) = use_state(1u32);
    let selected_user_value = selected_user.get();

    // Multiple independent futures
    let user_future = use_future(
        move || fetch_user_data(selected_user_value),
        selected_user_value,
    );

    let weather_future = use_future(
        || fetch_weather_data("New York".to_string()),
        (), // Run once on mount
    );

    let stats_future = use_future(
        || async {
            sleep(Duration::from_millis(400)).await;
            Ok::<String, String>("📊 1,234 active users, 567 online".to_string())
        },
        (),
    );

    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title="🎛️ Data Dashboard"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
            >
                <Text content="👥 Available users: 1, 2, 999 (999 will show error)" />
                <Text content="🔄 User data refreshes automatically" />
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(33),
                    Constraint::Percentage(33),
                    Constraint::Percentage(34),
                ]}
            >
                // User data panel
                <Block
                    title="👤 User Data"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Blue)}
                >
                    {match user_future.state() {
                        FutureState::Pending => rsx! {
                            <Text content="🔄 Loading..." />
                        },
                        FutureState::Resolved(user) => rsx! {
                            <Layout direction={Direction::Vertical}>
                                <Text content={format!("ID: {}", user.id)} />
                                <Text content={format!("Name: {}", user.name)} />
                            </Layout>
                        },
                        FutureState::Error(err) => rsx! {
                            <Text content={format!("❌ {}", err)} />
                        },
                    }}
                </Block>

                // Weather panel
                <Block
                    title="🌤️ Weather"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    {match weather_future.state() {
                        FutureState::Pending => rsx! {
                            <Text content="🔄 Loading..." />
                        },
                        FutureState::Resolved(weather) => rsx! {
                            <Text content={weather} />
                        },
                        FutureState::Error(err) => rsx! {
                            <Text content={format!("❌ {}", err)} />
                        },
                    }}
                </Block>

                // Stats panel
                <Block
                    title="📊 Statistics"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Magenta)}
                >
                    {match stats_future.state() {
                        FutureState::Pending => rsx! {
                            <Text content="🔄 Loading..." />
                        },
                        FutureState::Resolved(stats) => rsx! {
                            <Text content={stats} />
                        },
                        FutureState::Error(err) => rsx! {
                            <Text content={format!("❌ {}", err)} />
                        },
                    }}
                </Block>
            </Layout>
        </Layout>
    }
}

// Main application component
#[derive(Props, Debug, Clone)]
struct FutureDemoProps {}

#[component(FutureDemo)]
fn future_demo(_props: FutureDemoProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title="🚀 use_future Hook Demo"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
            >
                <Text content="This demo showcases the use_future hook with various async patterns:\n• Dependency-based re-execution\n• Error handling\n• Manual triggering\n• Concurrent futures\n\nPress 'q' or 'Esc' to quit" />
            </Block>

            <UserProfile user_id={123} />
            <WeatherWidget />
            <DataDashboard />
        </Layout>
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚀 Starting use_future hook demo...");

    let element = rsx! {
        <FutureDemo />
    };

    render_async(element).await
}
