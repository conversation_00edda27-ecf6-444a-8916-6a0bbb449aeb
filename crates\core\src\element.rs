use std::any::Any;
use std::fmt::Debug;

/// Trait for cloneable props that can be stored in TypedProps
pub trait CloneableProps: Any + Send + Sync + Debug {
    fn clone_box(&self) -> Box<dyn CloneableProps>;
    fn as_any(&self) -> &dyn Any;
}

impl<T: Any + Send + Sync + Debug + Clone + 'static> CloneableProps for T {
    fn clone_box(&self) -> Box<dyn CloneableProps> {
        Box::new(self.clone())
    }

    fn as_any(&self) -> &dyn Any {
        self
    }
}

/// Type-safe props container that can hold any props struct
#[derive(Debug)]
pub struct TypedProps {
    inner: Box<dyn CloneableProps>,
}

impl TypedProps {
    pub fn new<T: CloneableProps + 'static>(props: T) -> Self {
        Self {
            inner: Box::new(props),
        }
    }

    pub fn get<T: Any + 'static>(&self) -> Option<&T> {
        self.inner.as_any().downcast_ref::<T>()
    }
}

impl Clone for TypedProps {
    fn clone(&self) -> Self {
        Self {
            inner: self.inner.clone_box(),
        }
    }
}

/// Represents a virtual DOM node
#[derive(Debug, Clone)]
pub enum VirtualNode {
    /// A component element with typed props
    Component {
        name: String,
        props: TypedProps,
        children: Vec<VirtualNode>,
        render_fn: fn(
            &TypedProps,
            Option<&mut ratatui::Frame>,
            Option<ratatui::layout::Rect>,
        ) -> VirtualNode,
    },
    /// A text node
    Text(String),
    /// A widget element (maps to ratatui widgets)
    Widget {
        widget_type: WidgetType,
        props: TypedProps,
        children: Vec<VirtualNode>,
    },
}

/// Types of widgets supported by the framework
#[derive(Debug, Clone, PartialEq)]
pub enum WidgetType {
    Block,
    Text,
    Layout,
    Modal,
    ScrollArea,
    ScrollBar,
}

/// The main element type that components return
pub type Element = VirtualNode;

/// Children type for components, similar to Yew's Children
/// This represents child elements that can be passed to components
#[derive(Debug, Clone)]
pub struct Children {
    inner: Vec<Element>,
}

impl Children {
    /// Create new empty children
    pub fn new() -> Self {
        Self { inner: Vec::new() }
    }

    /// Create children from a vector of elements
    pub fn from_vec(elements: Vec<Element>) -> Self {
        Self { inner: elements }
    }

    /// Create children from a single element
    pub fn from_element(element: Element) -> Self {
        Self {
            inner: vec![element],
        }
    }

    /// Get the children as a slice
    pub fn as_slice(&self) -> &[Element] {
        &self.inner
    }

    /// Get the children as a vector (consuming self)
    pub fn into_vec(self) -> Vec<Element> {
        self.inner
    }

    /// Check if children is empty
    pub fn is_empty(&self) -> bool {
        self.inner.is_empty()
    }

    /// Get the number of children
    pub fn len(&self) -> usize {
        self.inner.len()
    }

    /// Iterate over children
    pub fn iter(&self) -> std::slice::Iter<Element> {
        self.inner.iter()
    }
}

impl Default for Children {
    fn default() -> Self {
        Self::new()
    }
}

impl PartialEq for Children {
    fn eq(&self, other: &Self) -> bool {
        self.inner.len() == other.inner.len()
            && self.inner.iter().zip(other.inner.iter()).all(|(a, b)| {
                // For now, we'll do a simple comparison based on the structure
                // This is a simplified implementation - in a real scenario you might want
                // more sophisticated comparison logic
                std::mem::discriminant(a) == std::mem::discriminant(b)
            })
    }
}

impl From<Vec<Element>> for Children {
    fn from(elements: Vec<Element>) -> Self {
        Self::from_vec(elements)
    }
}

impl From<Element> for Children {
    fn from(element: Element) -> Self {
        Self::from_element(element)
    }
}

impl From<Children> for Vec<Element> {
    fn from(children: Children) -> Self {
        children.into_vec()
    }
}

impl IntoElement for Children {
    fn into_element(self) -> Element {
        // Convert children to a single element
        // If empty, return empty text
        // If single element, return that element
        // If multiple elements, wrap in a layout
        match self.inner.len() {
            0 => VirtualNode::Text(String::new()),
            1 => self.inner.into_iter().next().unwrap(),
            _ => {
                // Create a layout to hold multiple elements
                use crate::{LayoutProps, WidgetType};
                use ratatui::layout::{Constraint, Direction};
                VirtualNode::widget(
                    WidgetType::Layout,
                    LayoutProps {
                        direction: Some(Direction::Vertical),
                        constraints: Some(vec![Constraint::Min(0); self.inner.len()]),
                        margin: None,
                    },
                    self.inner,
                )
            }
        }
    }
}

impl VirtualNode {
    /// Create a new component node
    pub fn component<T: CloneableProps + 'static>(
        name: impl Into<String>,
        props: T,
        children: Vec<VirtualNode>,
        render_fn: fn(
            &TypedProps,
            Option<&mut ratatui::Frame>,
            Option<ratatui::layout::Rect>,
        ) -> VirtualNode,
    ) -> Self {
        VirtualNode::Component {
            name: name.into(),
            props: TypedProps::new(props),
            children,
            render_fn,
        }
    }

    /// Create a new text node
    pub fn text(content: impl Into<String>) -> Self {
        VirtualNode::Text(content.into())
    }

    /// Create a new widget node
    pub fn widget<T: CloneableProps + 'static>(
        widget_type: WidgetType,
        props: T,
        children: Vec<VirtualNode>,
    ) -> Self {
        VirtualNode::Widget {
            widget_type,
            props: TypedProps::new(props),
            children,
        }
    }

    /// Get the children of this node
    pub fn children(&self) -> &[VirtualNode] {
        match self {
            VirtualNode::Component { children, .. } => children,
            VirtualNode::Widget { children, .. } => children,
            VirtualNode::Text(_) => &[],
        }
    }

    /// Get the typed props of this node
    pub fn typed_props(&self) -> Option<&TypedProps> {
        match self {
            VirtualNode::Component { props, .. } => Some(props),
            VirtualNode::Widget { props, .. } => Some(props),
            VirtualNode::Text(_) => None,
        }
    }
}

/// Trait for types that can be converted to Elements
pub trait IntoElement {
    fn into_element(self) -> Element;
}

impl IntoElement for Element {
    fn into_element(self) -> Element {
        self
    }
}

impl IntoElement for String {
    fn into_element(self) -> Element {
        VirtualNode::Text(self)
    }
}

impl IntoElement for &str {
    fn into_element(self) -> Element {
        VirtualNode::Text(self.to_string())
    }
}

impl<T: IntoElement> IntoElement for Vec<T> {
    fn into_element(self) -> Element {
        // For vectors, we'll create a container that holds all elements
        if self.is_empty() {
            VirtualNode::Text(String::new())
        } else if self.len() == 1 {
            self.into_iter().next().unwrap().into_element()
        } else {
            // Create a layout to hold multiple elements
            use crate::{LayoutProps, WidgetType};
            use ratatui::layout::{Constraint, Direction};
            VirtualNode::widget(
                WidgetType::Layout,
                LayoutProps {
                    direction: Some(Direction::Vertical),
                    constraints: Some(vec![Constraint::Min(0); self.len()]),
                    margin: None,
                },
                self.into_iter().map(|item| item.into_element()).collect(),
            )
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::borrow::Cow;

    #[test]
    fn test_into_element_for_str() {
        let element = "Hello, World!".into_element();
        match element {
            VirtualNode::Text(content) => {
                assert_eq!(content, "Hello, World!");
            }
            _ => panic!("Expected Text node"),
        }
    }

    #[test]
    fn test_into_element_for_string() {
        let text = "Hello, String!".to_string();
        let element = text.into_element();
        match element {
            VirtualNode::Text(content) => {
                assert_eq!(content, "Hello, String!");
            }
            _ => panic!("Expected Text node"),
        }
    }

    #[test]
    fn test_into_element_for_cow_str() {
        let cow_str = Cow::Borrowed("Hello, Cow!");
        let element = cow_str.into_element();
        match element {
            VirtualNode::Text(content) => {
                assert_eq!(content, "Hello, Cow!");
            }
            _ => panic!("Expected Text node"),
        }
    }

    #[test]
    fn test_into_element_for_empty_vec() {
        let empty_vec: Vec<&str> = vec![];
        let element = empty_vec.into_element();
        match element {
            VirtualNode::Text(content) => {
                assert_eq!(content, "");
            }
            _ => panic!("Expected empty Text node"),
        }
    }

    #[test]
    fn test_into_element_for_single_item_vec() {
        let single_vec = vec!["Single item"];
        let element = single_vec.into_element();
        match element {
            VirtualNode::Text(content) => {
                assert_eq!(content, "Single item");
            }
            _ => panic!("Expected Text node"),
        }
    }

    #[test]
    fn test_virtual_node_text_method_still_works() {
        let element = VirtualNode::text("Direct text creation");
        match element {
            VirtualNode::Text(content) => {
                assert_eq!(content, "Direct text creation");
            }
            _ => panic!("Expected Text node"),
        }
    }
}
