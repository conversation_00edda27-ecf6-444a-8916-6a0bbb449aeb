#[cfg(test)]
mod tests {
    use super::*;
    use crate::{ScrollState, ScrollOrientation, ScrollbarPosition, ScrollbarAppearance};

    #[test]
    fn test_scroll_state_creation() {
        let state = ScrollState::new(100, 20);
        assert_eq!(state.content_size, 100);
        assert_eq!(state.viewport_size, 20);
        assert_eq!(state.offset, 0);
    }

    #[test]
    fn test_scroll_state_needs_scrolling() {
        let state_no_scroll = ScrollState::new(10, 20);
        assert!(!state_no_scroll.needs_scrolling());

        let state_needs_scroll = ScrollState::new(100, 20);
        assert!(state_needs_scroll.needs_scrolling());
    }

    #[test]
    fn test_scroll_state_max_offset() {
        let state = ScrollState::new(100, 20);
        assert_eq!(state.max_offset(), 80);

        let state_no_scroll = ScrollState::new(10, 20);
        assert_eq!(state_no_scroll.max_offset(), 0);
    }

    #[test]
    fn test_scroll_state_scroll_to() {
        let mut state = ScrollState::new(100, 20);
        
        // Normal scroll
        state.scroll_to(50);
        assert_eq!(state.offset, 50);
        
        // Clamp to max
        state.scroll_to(200);
        assert_eq!(state.offset, 80);
        
        // Clamp to min
        state.scroll_to(0);
        assert_eq!(state.offset, 0);
    }

    #[test]
    fn test_scroll_state_scroll_by() {
        let mut state = ScrollState::new(100, 20);
        
        // Scroll down
        state.scroll_by(10);
        assert_eq!(state.offset, 10);
        
        // Scroll up
        state.scroll_by(-5);
        assert_eq!(state.offset, 5);
        
        // Clamp at boundaries
        state.scroll_by(-20);
        assert_eq!(state.offset, 0);
        
        state.scroll_by(200);
        assert_eq!(state.offset, 80);
    }

    #[test]
    fn test_scroll_state_thumb_info() {
        let state = ScrollState::new(100, 20);
        let (thumb_pos, thumb_size) = state.thumb_info(10);
        
        // Thumb size should be proportional to viewport/content ratio
        assert!(thumb_size > 0);
        assert!(thumb_size <= 10);
        assert!(thumb_pos <= 10 - thumb_size);
    }

    #[test]
    fn test_scrollbar_appearance_defaults() {
        let vertical = ScrollbarAppearance::vertical();
        assert_eq!(vertical.begin_symbol, "▲");
        assert_eq!(vertical.end_symbol, "▼");
        
        let horizontal = ScrollbarAppearance::horizontal();
        assert_eq!(horizontal.begin_symbol, "◀");
        assert_eq!(horizontal.end_symbol, "▶");
    }

    #[test]
    fn test_scroll_area_props_validation() {
        use crate::{ScrollAreaProps, Children, ComponentProps};
        
        let props = ScrollAreaProps {
            children: Children::default(),
            horizontal_scroll: None,
            vertical_scroll: None,
            show_horizontal_scrollbar: true,
            show_vertical_scrollbar: true,
            horizontal_scrollbar_position: ScrollbarPosition::Bottom,
            vertical_scrollbar_position: ScrollbarPosition::Right,
            horizontal_scrollbar_appearance: ScrollbarAppearance::horizontal(),
            vertical_scrollbar_appearance: ScrollbarAppearance::vertical(),
            content_style: None,
        };
        
        // Should validate successfully
        assert!(props.validate().is_ok());
    }

    #[test]
    fn test_scroll_area_props_invalid_positions() {
        use crate::{ScrollAreaProps, Children, ComponentProps};
        
        let props = ScrollAreaProps {
            children: Children::default(),
            horizontal_scroll: None,
            vertical_scroll: None,
            show_horizontal_scrollbar: true,
            show_vertical_scrollbar: true,
            horizontal_scrollbar_position: ScrollbarPosition::Left, // Invalid for horizontal
            vertical_scrollbar_position: ScrollbarPosition::Top,    // Invalid for vertical
            horizontal_scrollbar_appearance: ScrollbarAppearance::horizontal(),
            vertical_scrollbar_appearance: ScrollbarAppearance::vertical(),
            content_style: None,
        };
        
        // Should fail validation
        assert!(props.validate().is_err());
    }

    #[test]
    fn test_scroll_bar_props_validation() {
        use crate::{ScrollBarProps, ComponentProps};
        
        let props = ScrollBarProps {
            orientation: ScrollOrientation::Vertical,
            scroll_state: ScrollState::new(100, 20),
            position: ScrollbarPosition::Right,
            appearance: ScrollbarAppearance::vertical(),
            size: None,
        };
        
        // Should validate successfully
        assert!(props.validate().is_ok());
    }

    #[test]
    fn test_scroll_bar_props_invalid_position() {
        use crate::{ScrollBarProps, ComponentProps};
        
        let props = ScrollBarProps {
            orientation: ScrollOrientation::Vertical,
            scroll_state: ScrollState::new(100, 20),
            position: ScrollbarPosition::Top, // Invalid for vertical
            appearance: ScrollbarAppearance::vertical(),
            size: None,
        };
        
        // Should fail validation
        assert!(props.validate().is_err());
    }
}
