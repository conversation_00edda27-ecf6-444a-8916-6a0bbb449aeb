use terminus_ui::prelude::*;

// Define props for a Card component using #[derive(Props)]
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct CardProps {
    pub title: String,
    pub content: String,
    pub variant: String, // "primary", "secondary", "success", "warning", "danger"
    pub is_highlighted: bool,
}

// Define props for a custom Button component (renamed to avoid conflict with built-in ButtonProps)
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct CustomButtonProps {
    pub label: String,
    pub variant: String, // "primary", "secondary", "outline"
    pub is_disabled: bool,
}

// Define props for a Sidebar component
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct SidebarProps {
    pub title: String,
    pub items: Vec<String>,
    pub selected_index: usize,
}

// Define props for the main App component
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct AppProps {
    pub app_title: String,
    pub version: String,
}

// CustomButton component with typed props
#[component(CustomButton)]
fn custom_button(props: CustomButtonProps) -> Element {
    let (border_color, _bg_style) = match props.variant.as_str() {
        "primary" => (
            Color::Blue,
            Style::default().bg(Color::Blue).fg(Color::White),
        ),
        "secondary" => (
            Color::Gray,
            Style::default().bg(Color::Gray).fg(Color::Black),
        ),
        "outline" => (Color::Blue, Style::default().fg(Color::Blue)),
        _ => (Color::White, Style::default()),
    };

    let border_color = if props.is_disabled {
        Color::DarkGray
    } else {
        border_color
    };

    rsx! {
        <Block
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Text content={format!("[{}{}]",
                props.label,
                if props.is_disabled { " (disabled)" } else { "" }
            )} />
        </Block>
    }
}

// Card component with typed props and conditional styling
#[component(Card)]
fn card(props: CardProps) -> Element {
    let border_color = match props.variant.as_str() {
        "primary" => Color::Blue,
        "secondary" => Color::Gray,
        "success" => Color::Green,
        "warning" => Color::Yellow,
        "danger" => Color::Red,
        _ => Color::White,
    };

    let border_style = if props.is_highlighted {
        Style::default()
            .fg(border_color)
            .add_modifier(Modifier::BOLD)
    } else {
        Style::default().fg(border_color)
    };

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={border_style}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Min(1),     // Content
                    Constraint::Length(3),  // Button area
                ]}
            >
                <Text content={props.content} />
                <CustomButton
                    label={"Action".to_string()}
                    variant={props.variant.clone()}
                    is_disabled={false}
                />
            </Layout>
        </Block>
    }
}

// Sidebar component with list of items
#[component(Sidebar)]
fn sidebar(props: SidebarProps) -> Element {
    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={props.items.iter().map(|_| Constraint::Length(1)).collect()}
            >
                <Text content={props.items.iter().enumerate().map(|(i, item)| {
                    format!("{} {}",
                        if i == props.selected_index { ">" } else { " " },
                        item
                    )
                }).collect::<Vec<_>>().join("\n")} />
            </Layout>
        </Block>
    }
}

// Main App component demonstrating complex nested composition
#[component(App)]
fn app(props: AppProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={format!("{} v{}", props.app_title, props.version)}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Text content="React-like Terminal UI Framework with Typed Props" />
            </Block>

            // Main content area
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(25),  // Sidebar
                    Constraint::Percentage(75),  // Content
                ]}
            >
                // Sidebar
                <Sidebar
                    title={"Navigation".to_string()}
                    items={vec![
                        "Dashboard".to_string(),
                        "Users".to_string(),
                        "Settings".to_string(),
                        "Help".to_string(),
                    ]}
                    selected_index={0}
                />

                // Content area with cards
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Percentage(50),  // Top row
                        Constraint::Percentage(50),  // Bottom row
                    ]}
                >
                    // Top row of cards
                    <Layout
                        direction={Direction::Horizontal}
                        constraints={vec![
                            Constraint::Percentage(50),
                            Constraint::Percentage(50),
                        ]}
                    >
                        <Card
                            title={"Welcome".to_string()}
                            content={"This demonstrates typed props with #[derive(Props)] and #[component] macros!".to_string()}
                            variant={"primary".to_string()}
                            is_highlighted={true}
                        />

                        <Card
                            title={"Features".to_string()}
                            content={"✓ Type-safe props\n✓ Component composition\n✓ RSX syntax\n✓ No runtime errors".to_string()}
                            variant={"success".to_string()}
                            is_highlighted={false}
                        />
                    </Layout>

                    // Bottom row of cards
                    <Layout
                        direction={Direction::Horizontal}
                        constraints={vec![
                            Constraint::Percentage(33),
                            Constraint::Percentage(33),
                            Constraint::Percentage(34),
                        ]}
                    >
                        <Card
                            title={"Performance".to_string()}
                            content={"Zero-cost abstractions with compile-time type checking".to_string()}
                            variant={"warning".to_string()}
                            is_highlighted={false}
                        />

                        <Card
                            title={"Developer Experience".to_string()}
                            content={"IntelliSense support and familiar React patterns".to_string()}
                            variant={"secondary".to_string()}
                            is_highlighted={false}
                        />

                        <Card
                            title={"Status".to_string()}
                            content={"All systems operational! Ready for production use.".to_string()}
                            variant={"success".to_string()}
                            is_highlighted={true}
                        />
                    </Layout>
                </Layout>
            </Layout>

            // Footer
            <Block
                title="Controls"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Layout
                    direction={Direction::Horizontal}
                    constraints={vec![
                        Constraint::Percentage(33),
                        Constraint::Percentage(33),
                        Constraint::Percentage(34),
                    ]}
                >
                    <CustomButton
                        label={"Quit (q)".to_string()}
                        variant={"outline".to_string()}
                        is_disabled={false}
                    />
                    <CustomButton
                        label={"Refresh (r)".to_string()}
                        variant={"primary".to_string()}
                        is_disabled={false}
                    />
                    <CustomButton
                        label={"Help (h)".to_string()}
                        variant={"secondary".to_string()}
                        is_disabled={false}
                    />
                </Layout>
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_props = AppProps {
        app_title: "Terminus UI".to_string(),
        version: "1.0.0".to_string(),
    };

    let element = rsx! {
        <App
            app_title={app_props.app_title}
            version={app_props.version}
        />
    };

    render(element)
}
