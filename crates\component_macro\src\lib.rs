use proc_macro::TokenStream;
use quote::quote;
use syn::parse::{Pa<PERSON>, ParseStream, Result};
use syn::{Ident, ItemFn, Signature, Type, parse_macro_input};

mod analysis;
mod errors;
mod parsing;
mod validation;

use analysis::{Component<PERSON><PERSON><PERSON><PERSON>, ComponentGenerationContext, ContextParams};
use parsing::analyze_component_params;
use validation::ComponentValidator;

/// Arguments for the #[component] macro
#[derive(Default)]
struct ComponentArgs {
    /// Optional explicit component name
    component_name: Option<Ident>,
}

impl Parse for ComponentArgs {
    fn parse(input: ParseStream) -> Result<Self> {
        if input.is_empty() {
            // No arguments provided
            return Ok(ComponentArgs::default());
        }

        // Parse the component name identifier
        let component_name = input.parse::<Ident>()?;

        // Ensure no trailing tokens
        if !input.is_empty() {
            return Err(input.error("unexpected token after component name"));
        }

        Ok(ComponentArgs {
            component_name: Some(component_name),
        })
    }
}

/// The #[component] attribute macro for creating functional components
#[proc_macro_attribute]
pub fn component(args: TokenStream, input: TokenStream) -> TokenStream {
    let args = parse_macro_input!(args as ComponentArgs);
    let input_fn = parse_macro_input!(input as ItemFn);

    let output = generate_component_code(&input_fn, args);
    output.into()
}

/// Generate code for a component function
fn generate_component_code(input_fn: &ItemFn, args: ComponentArgs) -> proc_macro2::TokenStream {
    let ItemFn {
        attrs,
        vis,
        sig,
        block,
    } = input_fn;

    let Signature {
        ident: fn_name,
        inputs,
        output,
        generics,
        ..
    } = sig;

    // Perform early validation for quick feedback
    if let Err(error) = ComponentValidator::early_validate_signature(inputs, output) {
        return error.to_syn_error().to_compile_error();
    }

    // Validate component naming conventions
    if let Err(naming_error) = ComponentAnalyzer::validate_component_naming(fn_name) {
        return quote::quote! {
            compile_error!(#naming_error);
        };
    }

    // Analyze component parameters
    let params = match analyze_component_params(inputs) {
        Ok(params) => params,
        Err(error) => return error.to_syn_error().to_compile_error(),
    };

    let default_props_type: Type = syn::parse_quote! { () };
    let props_type = params.props_param.as_ref().unwrap_or(&default_props_type);

    // Generate the component struct name - use explicit name if provided, otherwise generate from function name
    let component_name = args
        .component_name
        .unwrap_or_else(|| ComponentAnalyzer::generate_component_name(fn_name));

    // Validate component signature and generate validation code
    let props_validation = match ComponentValidator::validate_component_signature(
        &params,
        output,
        &component_name,
        props_type,
    ) {
        Ok(validation) => validation,
        Err(error) => return error.to_syn_error().to_compile_error(),
    };

    // Create context for component generation
    let ctx = ComponentGenerationContext::new(ContextParams {
        params: &params,
        fn_name,
        component_name: &component_name,
        props_type,
        attrs,
        vis,
        generics,
        inputs,
        output,
        block,
        props_validation: &props_validation,
    });

    // Generate unified component implementation for all types
    generate_unified_component(&ctx)
}

/// Generate unified component implementation for all component types
fn generate_unified_component(ctx: &ComponentGenerationContext) -> proc_macro2::TokenStream {
    let ComponentGenerationContext {
        fn_name,
        component_name,
        props_type,
        attrs,
        vis,
        inputs,
        output,
        block,
        props_validation,
        ..
    } = ctx;

    // Generate enhanced documentation based on component analysis
    let complexity_docs = ctx.generate_complexity_docs();
    let metadata_docs = ctx.generate_metadata_docs();
    let generic_constraints = ctx.generate_generic_constraints();

    // Generate the appropriate render implementation based on component signature
    let component_type = ctx.component_type_str();

    let render_impl = match component_type {
        "FrameAreaComponent" => {
            quote! {
                fn render(
                    props: &Self::Properties,
                    frame: Option<&mut Frame>,
                    area: Option<Rect>,
                ) -> Element {
                    match (frame, area) {
                        (Some(frame), Some(area)) => Self::render(props.clone(), frame, area),
                        _ => VirtualNode::Text(format!(
                            "Component {} requires both frame and area access",
                            stringify!(#fn_name)
                        )),
                    }
                }
            }
        }
        "FrameComponent" => {
            quote! {
                fn render(
                    props: &Self::Properties,
                    frame: Option<&mut Frame>,
                    area: Option<Rect>,
                ) -> Element {
                    match frame {
                        Some(frame) => Self::render(props.clone(), frame),
                        None => VirtualNode::Text(format!(
                            "Component {} requires frame access",
                            stringify!(#fn_name)
                        )),
                    }
                }
            }
        }
        "AreaComponent" => {
            quote! {
                fn render(
                    props: &Self::Properties,
                    frame: Option<&mut Frame>,
                    area: Option<Rect>,
                ) -> Element {
                    match area {
                        Some(area) => Self::render(props.clone(), area),
                        None => VirtualNode::Text(format!(
                            "Component {} requires area access",
                            stringify!(#fn_name)
                        )),
                    }
                }
            }
        }
        _ => {
            quote! {
                fn render(
                    props: &Self::Properties,
                    _frame: Option<&mut Frame>,
                    _area: Option<Rect>,
                ) -> Element {
                    // Basic component - ignore frame and area parameters
                    Self::render(props.clone())
                }
            }
        }
    };

    quote! {
        // Include compile-time props validation
        #props_validation

        #complexity_docs
        #metadata_docs
        #(#attrs)*
        #vis struct #component_name #generic_constraints;

        impl #generic_constraints #component_name #generic_constraints {
            #vis fn render(#inputs) #output #block
        }

        impl #generic_constraints FunctionalComponent for #component_name #generic_constraints {
            type Properties = #props_type;

            #render_impl

            fn component_name() -> &'static str {
                stringify!(#fn_name)
            }
        }

        // Generate a helper function for creating TypedComponent nodes
        impl #generic_constraints #component_name #generic_constraints {
            pub fn create_typed_element(props: #props_type) -> Element {
                <Self as FunctionalComponent>::create_element(props)
            }

            pub fn create_element_with_children(props: #props_type, children: Vec<Element>) -> Element {
                <Self as FunctionalComponent>::create_element_with_children(props, children)
            }
        }

        // Re-export the original function for direct use
        #[allow(non_snake_case)]
        #(#attrs)*
        #vis fn #fn_name(#inputs) #output #block
    }
}
