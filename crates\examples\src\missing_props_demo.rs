use terminus_ui::prelude::*;

// Example component with required props (non-Option fields)
#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>lone)]
pub struct UserProfileProps {
    pub name: String,          // Required field
    pub age: u32,              // Required field
    pub email: Option<String>, // Optional field
    pub bio: Option<String>,   // Optional field
}

// Component that uses the props
#[component(UserProfile)]
fn user_profile(props: UserProfileProps) -> Element {
    rsx! {
        <Block
            title={format!("Profile: {}", props.name)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("Name: {}", props.name)} />
                <Text content={format!("Age: {}", props.age)} />
                <Text content={format!("Email: {}",
                    props.email.as_deref().unwrap_or("Not provided"))} />
                <Text content={format!("Bio: {}",
                    props.bio.as_deref().unwrap_or("No bio available"))} />
            </Layout>
        </Block>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // This works fine - all props provided (including optional ones)
    let element_good = rsx! {
        <UserProfile
            name={"John Doe".to_string()}
            age={30}
            // email={Some("<EMAIL>".to_string())}
            // bio={Some("Software developer with 5 years of experience.".to_string())}
        />
    };

    // Uncomment the following to see improved error messages for missing required props:

    // This should fail with a clear error message about missing required props
    // let element_bad = rsx! {
    //     <UserProfile
    //         // name is missing - should cause a clear compilation error
    //         age={30}
    //         email={Some("<EMAIL>".to_string())}
    //     />
    // };

    // This should also fail with a clear error message
    // let element_bad2 = rsx! {
    //     <UserProfile
    //         name={"Jane Doe".to_string()}
    //         // age is missing - should cause a clear compilation error
    //         email={Some("<EMAIL>".to_string())}
    //     />
    // };

    // This should fail because no props are provided at all
    // let element_bad3 = rsx! {
    //     <UserProfile />
    // };

    render(element_good)
}

#[cfg(test)]
mod tests {
    use super::*;
    use terminus_ui::PropRequirements;

    #[test]
    fn test_valid_props() {
        // This should compile fine
        let props = UserProfileProps {
            name: "Test User".to_string(),
            age: 25,
            email: Some("<EMAIL>".to_string()),
            bio: None,
        };

        let _element = UserProfile::create_element(props);
    }

    #[test]
    fn test_props_validation() {
        let props = UserProfileProps {
            name: "Test User".to_string(),
            age: 25,
            email: Some("<EMAIL>".to_string()),
            bio: None,
        };

        // Test that validation passes
        assert!(props.validate().is_ok());
    }

    #[test]
    fn test_prop_requirements() {
        // Test that the PropRequirements trait is implemented correctly
        let required_fields = UserProfileProps::required_fields();
        assert!(required_fields.contains(&"name"));
        assert!(required_fields.contains(&"age"));
        assert!(!required_fields.contains(&"email")); // Optional field
        assert!(!required_fields.contains(&"bio")); // Optional field
    }

    // Uncomment these tests to see compile-time validation in action:

    // #[test]
    // fn test_missing_required_props_compilation_error() {
    //     // This should fail to compile with a helpful error message
    //     let element = rsx! {
    //         <UserProfile
    //             // Missing required 'name' and 'age' fields
    //             email={Some("<EMAIL>".to_string())}
    //         />
    //     };
    // }

    // #[test]
    // fn test_no_props_compilation_error() {
    //     // This should fail to compile with a helpful error message
    //     let element = rsx! {
    //         <UserProfile />
    //     };
    // }
}
