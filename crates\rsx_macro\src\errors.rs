use proc_macro2::Ident;
use proc_macro2::Span;
use syn::Error;

/// Custom error types for RSX macro with rich diagnostics
#[derive(Debug)]
pub enum RsxMacroError {
    /// Invalid RSX syntax
    Syntax { span: Span, message: String },
    /// Invalid element name
    ElementName { span: Span, found_name: String },
    /// Invalid attribute syntax
    AttributeSyntax {
        span: Span,
        attribute_name: Ident,
        issue: String,
    },
    // /// Invalid expression in RSX
    // Expression {
    //     span: Span,
    //     expression_type: String,
    // },
    // /// Invalid nesting structure
    // Nesting {
    //     span: Span,
    //     parent: String,
    //     child: String,
    // },
    // /// Missing required attribute
    // MissingAttribute {
    //     span: Span,
    //     element_name: String,
    //     attribute_name: String,
    // },
}

impl RsxMacroError {
    /// Get validation suggestions for this error type
    /// This integrates with the validation system to provide actionable guidance
    pub fn get_suggestions(&self) -> Vec<String> {
        match self {
            RsxMacroError::Syntax { .. } => vec![
                "Check RSX syntax follows JSX-like format".to_string(),
                "Ensure proper opening and closing tags".to_string(),
                "Example: rsx! { <Text content=\"Hello\" /> }".to_string(),
            ],
            RsxMacroError::ElementName { .. } => vec![
                "Use PascalCase for component names".to_string(),
                "Ensure the component is imported and available".to_string(),
                "Example: <Text>, <MyCustomComponent>".to_string(),
            ],
            RsxMacroError::AttributeSyntax { .. } => vec![
                "Use proper attribute syntax: name=\"value\" or name={expression}".to_string(),
                "String literals should be quoted".to_string(),
                "Expressions should be wrapped in braces".to_string(),
                "Example: <Text content=\"Hello\" style={my_style} />".to_string(),
            ],
            // RsxMacroError::Expression { .. } => vec![
            //     "Ensure expressions evaluate to valid types".to_string(),
            //     "Use proper Rust syntax within braces".to_string(),
            //     "Example: {variable}, {function_call()}, {if condition { value } else { other }}".to_string(),
            // ],
            // RsxMacroError::Nesting { .. } => vec![
            //     "Check element nesting rules".to_string(),
            //     "Some elements cannot contain certain children".to_string(),
            //     "Refer to component documentation for valid children".to_string(),
            // ],
            // RsxMacroError::MissingAttribute { .. } => vec![
            //     "Add the required attribute to the element".to_string(),
            //     "Check component documentation for required props".to_string(),
            //     "Use proper attribute syntax with quotes or braces".to_string(),
            // ],
        }
    }

    /// Convert to syn::Error with rich diagnostic messages
    /// This method automatically includes suggestions from get_suggestions()
    pub fn to_syn_error(&self) -> Error {
        // Get suggestions for this error type before consuming self
        let suggestions = self.get_suggestions();
        let suggestions_text = if suggestions.is_empty() {
            String::new()
        } else {
            format!(
                "\n\nSuggestions:\n{}",
                suggestions
                    .iter()
                    .enumerate()
                    .map(|(i, s)| format!("  {}. {}", i + 1, s))
                    .collect::<Vec<_>>()
                    .join("\n")
            )
        };

        match self {
            RsxMacroError::Syntax { span, message } => Error::new(
                *span,
                format!(
                    "Invalid RSX syntax: {}\n\
                    \n\
                    Help: RSX uses JSX-like syntax for creating elements:\n\
                    rsx! {{\n\
                        <ElementName attribute=\"value\">\n\
                            <ChildElement />\n\
                        </ElementName>\n\
                    }}{}",
                    message, suggestions_text
                ),
            ),
            RsxMacroError::ElementName { span, found_name } => Error::new(
                *span,
                format!(
                    "Invalid element name: '{}'\n\
                    \n\
                    Help: Element names should be:\n\
                    - PascalCase for components (Text, MyComponent)\n\
                    - Available in scope (imported or defined)\n\
                    - Valid Rust identifiers{}",
                    found_name, suggestions_text
                ),
            ),
            RsxMacroError::AttributeSyntax {
                span,
                attribute_name,
                issue,
            } => Error::new(
                *span,
                format!(
                    "Invalid attribute syntax for '{}': {}\n\
                    \n\
                    Help: Attributes should use one of these formats:\n\
                    - String literals: attribute=\"value\"\n\
                    - Expressions: attribute={{expression}}\n\
                    - Boolean: attribute={{true}} or attribute={{false}}{}",
                    attribute_name, issue, suggestions_text
                ),
            ),
            // RsxMacroError::Expression { span, expression_type } => Error::new(
            //     span,
            //     format!(
            //         "Invalid expression in RSX: {}\n\
            //         \n\
            //         Help: Expressions in RSX should:\n\
            //         - Be valid Rust expressions\n\
            //         - Evaluate to appropriate types\n\
            //         - Be wrapped in braces: {{expression}}{}",
            //         expression_type, suggestions_text
            //     ),
            // ),
            // RsxMacroError::Nesting { span, parent, child } => Error::new(
            //     span,
            //     format!(
            //         "Invalid nesting: '{}' cannot contain '{}'\n\
            //         \n\
            //         Help: Check component documentation for:\n\
            //         - Valid child elements\n\
            //         - Nesting restrictions\n\
            //         - Alternative component structures{}",
            //         parent, child, suggestions_text
            //     ),
            // ),
            // RsxMacroError::MissingAttribute { span, element_name, attribute_name } => Error::new(
            //     span,
            //     format!(
            //         "Missing required attribute '{}' for element '{}'\n\
            //         \n\
            //         Help: Add the required attribute:\n\
            //         <{} {}=\"value\" />\n\
            //         or\n\
            //         <{} {}={{expression}} />{}",
            //         attribute_name, element_name, element_name, attribute_name,
            //         element_name, attribute_name, suggestions_text
            //     ),
            // ),
        }
    }

    /// Create a syntax error
    pub fn syntax_error(span: Span, message: impl Into<String>) -> Self {
        Self::Syntax {
            span,
            message: message.into(),
        }
    }

    /// Create an element name error
    pub fn element_name_error(span: Span, found_name: impl Into<String>) -> Self {
        Self::ElementName {
            span,
            found_name: found_name.into(),
        }
    }

    /// Create an attribute syntax error
    pub fn attribute_syntax_error(
        span: Span,
        attribute_name: &proc_macro2::Ident,
        issue: impl Into<String>,
    ) -> Self {
        Self::AttributeSyntax {
            span,
            attribute_name: attribute_name.clone(),
            issue: issue.into(),
        }
    }

    // /// Create an expression error
    // pub fn expression_error(span: Span, expression_type: impl Into<String>) -> Self {
    //     Self::Expression {
    //         span,
    //         expression_type: expression_type.into(),
    //     }
    // }

    // /// Create a nesting error
    // pub fn nesting_error(
    //     span: Span,
    //     parent: impl Into<String>,
    //     child: impl Into<String>,
    // ) -> Self {
    //     Self::Nesting {
    //         span,
    //         parent: parent.into(),
    //         child: child.into(),
    //     }
    // }

    // /// Create a missing attribute error
    // pub fn missing_attribute_error(
    //     span: Span,
    //     element_name: impl Into<String>,
    //     attribute_name: impl Into<String>,
    // ) -> Self {
    //     Self::MissingAttribute {
    //         span,
    //         element_name: element_name.into(),
    //         attribute_name: attribute_name.into(),
    //     }
    // }
}

/// Result type for RSX macro operations
pub type RsxResult<T> = Result<T, RsxMacroError>;
