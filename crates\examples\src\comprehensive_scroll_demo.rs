use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Comprehensive demonstration of the ScrollArea component
/// This example shows various scrolling patterns and configurations

#[derive(Props, Debug, Clone)]
pub struct ScrollDemoProps {
    pub title: String,
}

/// Main demo component showcasing different ScrollArea configurations
#[component(ScrollDemo)]
fn scroll_demo(props: ScrollDemoProps) -> Element {
    let (selected_demo, set_selected_demo) = use_state(0usize);
    let (scroll_callback_info, set_scroll_callback_info) =
        use_state("No scroll events yet".to_string());

    // Handle demo navigation
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Tab => {
                    let next_demo = (selected_demo.get() + 1) % 4;
                    set_selected_demo.set(next_demo);
                }
                KeyCode::BackTab => {
                    let prev_demo = if selected_demo.get() == 0 {
                        3
                    } else {
                        selected_demo.get() - 1
                    };
                    set_selected_demo.set(prev_demo);
                }
                KeyCode::Char('q') => {
                    // Exit demo (this would be handled by the main loop)
                }
                _ => {}
            }
        }
    }

    // Scroll callback for demo 4
    let on_scroll = Callback::from({
        let set_info = set_scroll_callback_info.clone();
        move |pos: (usize, usize)| {
            set_info.set(format!("Scroll position: Y={}, X={}", pos.0, pos.1));
        }
    });

    rsx! {
        <Layout direction={Direction::Vertical} constraints={vec![
            Constraint::Length(3),  // Header
            Constraint::Min(0),     // Content
            Constraint::Length(3),  // Footer
        ]}>
            // Header
            <Block title={props.title} borders={Borders::ALL}>
                <Text content={format!("Demo {}/4 - Use Tab/Shift+Tab to navigate, Q to quit", selected_demo.get() + 1)} />
            </Block>

            // Main content area
            {match selected_demo.get() {
                0 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // Vertical scrolling demo
                        <Block title="Vertical Scrolling" borders={Borders::ALL}>
                            <ScrollArea
                                show_vertical_scrollbar={Some(true)}
                                vertical_scrollbar_position={Some(ScrollbarPosition::Right)}
                                vertical_scrollbar_appearance={Some(ScrollbarAppearance {
                                    begin_symbol: "↑".to_string(),
                                    end_symbol: "↓".to_string(),
                                    track_symbol: "│".to_string(),
                                    thumb_symbol: "█".to_string(),
                                    track_style: None,
                                    thumb_style: None,
                                    symbol_style: None,
                                })}
                            >
                                <Text content={generate_long_text("Vertical Content", 50)} />
                            </ScrollArea>
                        </Block>

                        // Horizontal scrolling demo
                        <Block title="Horizontal Scrolling" borders={Borders::ALL}>
                            <ScrollArea
                                show_horizontal_scrollbar={Some(true)}
                                horizontal_scrollbar_position={Some(ScrollbarPosition::Bottom)}
                                horizontal_scrollbar_appearance={Some(ScrollbarAppearance {
                                    begin_symbol: "←".to_string(),
                                    end_symbol: "→".to_string(),
                                    track_symbol: "─".to_string(),
                                    thumb_symbol: "█".to_string(),
                                    track_style: None,
                                    thumb_style: None,
                                    symbol_style: None,
                                })}
                            >
                                <Text content={generate_wide_text("Horizontal Content", 10)} />
                            </ScrollArea>
                        </Block>
                    </Layout>
                },
                1 => rsx! {
                    <Block title="Both Directions Scrolling" borders={Borders::ALL}>
                        <ScrollArea
                            show_horizontal_scrollbar={Some(true)}
                            show_vertical_scrollbar={Some(true)}
                            vertical_scrollbar_position={Some(ScrollbarPosition::Right)}
                            horizontal_scrollbar_position={Some(ScrollbarPosition::Bottom)}
                        >
                            <Text content={generate_large_text("Both Directions", 30, 20)} />
                        </ScrollArea>
                    </Block>
                },
                2 => rsx! {
                    <Layout direction={Direction::Horizontal} constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50),
                    ]}>
                        // With scrollbar
                        <Block title="With Scrollbar" borders={Borders::ALL}>
                            <ScrollArea
                                show_vertical_scrollbar={Some(true)}
                                vertical_scrollbar_position={Some(ScrollbarPosition::Right)}
                            >
                                <Text content={generate_long_text("Scrollbar Visible", 30)} />
                            </ScrollArea>
                        </Block>

                        // Without scrollbar
                        <Block title="Without Scrollbar" borders={Borders::ALL}>
                            <ScrollArea
                                show_vertical_scrollbar={Some(false)}
                            >
                                <Text content={generate_long_text("No Scrollbar", 30)} />
                            </ScrollArea>
                        </Block>
                    </Layout>
                },
                3 => rsx! {
                    <Layout direction={Direction::Vertical} constraints={vec![
                        Constraint::Length(3),
                        Constraint::Min(0),
                    ]}>
                        // Callback info
                        <Block title="Scroll Callback Demo" borders={Borders::ALL}>
                            <Text content={scroll_callback_info.get()} />
                        </Block>

                        // Scrollable area with callback
                        <Block title="Scroll to see callback in action" borders={Borders::ALL}>
                            <ScrollArea
                                show_vertical_scrollbar={Some(true)}
                                vertical_scrollbar_position={Some(ScrollbarPosition::Right)}
                            >
                                <Text content={generate_long_text("Callback Content", 40)} />
                            </ScrollArea>
                        </Block>
                    </Layout>
                },
                _ => rsx! {
                    <Text content="Invalid demo selection" />
                },
            }}

            // Footer with instructions
            <Block borders={Borders::TOP}>
                <Text content="Controls: ↑/↓/←/→ = Scroll, Page Up/Down = Fast scroll, Home/End = Jump, Tab = Next demo, Q = Quit" />
            </Block>
        </Layout>
    }
}

/// Generate long text for vertical scrolling demos
fn generate_long_text(prefix: &str, lines: usize) -> String {
    (1..=lines)
        .map(|i| {
            format!(
                "{} line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                prefix, i
            )
        })
        .collect::<Vec<_>>()
        .join("\n")
}

/// Generate wide text for horizontal scrolling demos
fn generate_wide_text(prefix: &str, lines: usize) -> String {
    (1..=lines)
        .map(|i| format!("{} line {} - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities in the ScrollArea component.", prefix, i))
        .collect::<Vec<_>>()
        .join("\n")
}

/// Generate large text for both-directions scrolling
fn generate_large_text(prefix: &str, lines: usize, line_length: usize) -> String {
    (1..=lines)
        .map(|i| {
            let base_text = format!("{} line {} - ", prefix, i);
            let padding = "Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ";
            let repeated_padding = padding.repeat((line_length * 10) / padding.len() + 1);
            format!("{}{}", base_text, &repeated_padding[..line_length.min(repeated_padding.len())])
        })
        .collect::<Vec<_>>()
        .join("\n")
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo_props = ScrollDemoProps {
        title: "🔄 ScrollArea Component Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <ScrollDemo title={demo_props.title} />
    };

    render(element)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_long_text() {
        let text = generate_long_text("Test", 3);
        let lines: Vec<&str> = text.lines().collect();
        assert_eq!(lines.len(), 3);
        assert!(lines[0].contains("Test line 1"));
        assert!(lines[1].contains("Test line 2"));
        assert!(lines[2].contains("Test line 3"));
    }

    #[test]
    fn test_generate_wide_text() {
        let text = generate_wide_text("Wide", 2);
        let lines: Vec<&str> = text.lines().collect();
        assert_eq!(lines.len(), 2);
        assert!(lines[0].len() > 100); // Should be quite long
        assert!(lines[0].contains("Wide line 1"));
    }

    #[test]
    fn test_generate_large_text() {
        let text = generate_large_text("Large", 2, 50);
        let lines: Vec<&str> = text.lines().collect();
        assert_eq!(lines.len(), 2);
        assert!(lines[0].len() >= 50); // Should be at least the requested length
        assert!(lines[0].contains("Large line 1"));
    }
}
