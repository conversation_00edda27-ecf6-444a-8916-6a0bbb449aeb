use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Comprehensive demonstration of the ScrollArea component
/// This example shows various scrolling patterns and configurations

#[derive(Props, Debug, Clone)]
pub struct ScrollDemoProps {
    pub title: String,
}

/// Main demo component showcasing different ScrollArea configurations
#[component(ScrollDemo)]
fn scroll_demo(_props: ScrollDemoProps) -> Element {
    let (selected_demo, set_selected_demo) = use_state(0usize);
    let (scroll_callback_info, set_scroll_callback_info) =
        use_state("No scroll events yet".to_string());

    // Handle demo navigation
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            match key.code {
                KeyCode::Tab => {
                    let next_demo = (selected_demo.get() + 1) % 4;
                    set_selected_demo.set(next_demo);
                }
                KeyCode::BackTab => {
                    let prev_demo = if selected_demo.get() == 0 {
                        3
                    } else {
                        selected_demo.get() - 1
                    };
                    set_selected_demo.set(prev_demo);
                }
                KeyCode::Char('q') => {
                    // Exit demo (this would be handled by the main loop)
                }
                _ => {}
            }
        }
    }

    // Scroll callback for demo 4 (not yet implemented in ScrollArea)
    let _on_scroll = Callback::from({
        let set_info = set_scroll_callback_info.clone();
        move |pos: (usize, usize)| {
            set_info.set(format!("Scroll position: Y={}, X={}", pos.0, pos.1));
        }
    });

    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={format!("🔄 ScrollArea Demo {}/4", selected_demo.get() + 1)}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Use Tab/Shift+Tab to navigate demos, Arrow keys to scroll, Q to quit" />
            </Block>

            // Main content area
            {match selected_demo.get() {
                0 => rsx! {
                    <Layout direction={Direction::Horizontal}>
                        // Vertical scrolling demo
                        <Block title="Vertical Scrolling" borders={Borders::ALL} border_style={Style::default().fg(Color::Green)}>
                            <ScrollArea>
                                {(1..=50).map(|i| rsx! {
                                    <Text content={format!("Vertical line {} - Lorem ipsum dolor sit amet, consectetur adipiscing elit.", i)} />
                                }).collect::<Vec<_>>()}
                            </ScrollArea>
                        </Block>

                        // Horizontal scrolling demo
                        <Block title="Horizontal Scrolling" borders={Borders::ALL} border_style={Style::default().fg(Color::Yellow)}>
                            <ScrollArea>
                                {(1..=15).map(|i| rsx! {
                                    <Text content={format!("Horizontal line {} - This is a very long line that should extend beyond the normal terminal width to demonstrate horizontal scrolling capabilities.", i)} />
                                }).collect::<Vec<_>>()}
                            </ScrollArea>
                        </Block>
                    </Layout>
                },
                1 => rsx! {
                    <Block title="Both Directions Scrolling" borders={Borders::ALL} border_style={Style::default().fg(Color::Magenta)}>
                        <ScrollArea>
                            {(1..=40).map(|i| rsx! {
                                <Text content={format!("Both directions line {} - Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua ut enim ad minim veniam quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.", i)} />
                            }).collect::<Vec<_>>()}
                        </ScrollArea>
                    </Block>
                },
                2 => rsx! {
                    <Layout direction={Direction::Horizontal}>
                        // With scrollbar
                        <Block title="With Scrollbar" borders={Borders::ALL} border_style={Style::default().fg(Color::Blue)}>
                            <ScrollArea show_vertical_scrollbar={Some(true)}>
                                {(1..=30).map(|i| rsx! {
                                    <Text content={format!("Scrollbar visible line {} - Content with scrollbar shown", i)} />
                                }).collect::<Vec<_>>()}
                            </ScrollArea>
                        </Block>

                        // Without scrollbar
                        <Block title="Without Scrollbar" borders={Borders::ALL} border_style={Style::default().fg(Color::Red)}>
                            <ScrollArea show_vertical_scrollbar={Some(false)}>
                                {(1..=30).map(|i| rsx! {
                                    <Text content={format!("No scrollbar line {} - Content with scrollbar hidden", i)} />
                                }).collect::<Vec<_>>()}
                            </ScrollArea>
                        </Block>
                    </Layout>
                },
                3 => rsx! {
                    <Layout direction={Direction::Vertical}>
                        // Callback info
                        <Block title="Scroll Callback Info" borders={Borders::ALL} border_style={Style::default().fg(Color::White)}>
                            <Text content={scroll_callback_info.get()} />
                        </Block>

                        // Scrollable area with callback
                        <Block title="Custom Scrollbar Styling" borders={Borders::ALL} border_style={Style::default().fg(Color::LightBlue)}>
                            <ScrollArea
                                vertical_scrollbar_position={Some(ScrollbarPosition::Left)}
                                vertical_scrollbar_appearance={Some(ScrollbarAppearance {
                                    begin_symbol: "↑".to_string(),
                                    end_symbol: "↓".to_string(),
                                    track_symbol: "┃".to_string(),
                                    thumb_symbol: "█".to_string(),
                                    track_style: Some(Style::default().fg(Color::DarkGray)),
                                    thumb_style: Some(Style::default().fg(Color::White).bg(Color::Blue)),
                                    symbol_style: Some(Style::default().fg(Color::Cyan)),
                                })}
                            >
                                {(1..=50).map(|i| rsx! {
                                    <Text content={format!("Custom scrollbar line {} - Notice the scrollbar on the left with custom styling", i)} />
                                }).collect::<Vec<_>>()}
                            </ScrollArea>
                        </Block>
                    </Layout>
                },
                _ => rsx! {
                    <Block title="Error" borders={Borders::ALL}>
                        <Text content="Invalid demo selection" />
                    </Block>
                },
            }}

            // Footer with instructions
            <Block title="Controls" borders={Borders::ALL} border_style={Style::default().fg(Color::Gray)}>
                <Text content="↑/↓/←/→ = Scroll | Page Up/Down = Fast scroll | Home/End = Jump | Tab = Next demo | Q = Quit" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let demo_props = ScrollDemoProps {
        title: "🔄 ScrollArea Component Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <ScrollDemo title={demo_props.title} />
    };

    render(element)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_long_text() {
        let text = generate_long_text("Test", 3);
        let lines: Vec<&str> = text.lines().collect();
        assert_eq!(lines.len(), 3);
        assert!(lines[0].contains("Test line 1"));
        assert!(lines[1].contains("Test line 2"));
        assert!(lines[2].contains("Test line 3"));
    }

    #[test]
    fn test_generate_wide_text() {
        let text = generate_wide_text("Wide", 2);
        let lines: Vec<&str> = text.lines().collect();
        assert_eq!(lines.len(), 2);
        assert!(lines[0].len() > 100); // Should be quite long
        assert!(lines[0].contains("Wide line 1"));
    }

    #[test]
    fn test_generate_large_text() {
        let text = generate_large_text("Large", 2, 50);
        let lines: Vec<&str> = text.lines().collect();
        assert_eq!(lines.len(), 2);
        assert!(lines[0].len() >= 50); // Should be at least the requested length
        assert!(lines[0].contains("Large line 1"));
    }
}
