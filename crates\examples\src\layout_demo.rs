use terminus_ui::prelude::*;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let element = rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Length(3),  // Header
                Constraint::Min(0),     // Body
                Constraint::Length(3),  // Footer
            ]}
        >
            // Header
            <Block
                title="Header"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Terminus UI - React-like Terminal Framework" />
            </Block>

            // Body with horizontal layout
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(30),  // Sidebar
                    Constraint::Percentage(70),  // Content
                ]}
            >
                // Sidebar
                <Block
                    title="Sidebar"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green)}
                >
                    <Text content="Navigation Menu" />
                </Block>

                // Content area
                <Block
                    title="Main Content"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Magenta)}
                >
                    <Text content="This demonstrates the new typed props system!" />
                </Block>
            </Layout>

            // Footer
            <Block
                title="Footer"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="Press 'q' or Esc to quit" />
            </Block>
        </Layout>
    };

    render(element)
}
