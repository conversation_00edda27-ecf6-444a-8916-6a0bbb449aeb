use crate::{BlockProps, Element, LayoutProps, TextProps, TypedProps, VirtualNode, WidgetType};
use ratatui::{
    layout::{Constraint, Direction, Layout, Rect},
    style::Style,
    widgets::{Block, Borders, Paragraph, Widget},
};

/// Create a Block widget
pub fn block() -> BlockBuilder {
    BlockBuilder::new()
}

/// Create a Text widget
pub fn text(content: impl Into<String>) -> Element {
    let props = TextProps {
        content: content.into(),
        style: None,
    };
    VirtualNode::widget(WidgetType::Text, props, vec![])
}

/// Create a Layout widget
pub fn layout() -> LayoutBuilder {
    LayoutBuilder::new()
}

/// Builder for Block widgets
pub struct BlockBuilder {
    props: BlockProps,
    children: Vec<Element>,
}

impl BlockBuilder {
    pub fn new() -> Self {
        Self {
            props: BlockProps::default(),
            children: vec![],
        }
    }

    pub fn title(mut self, title: impl Into<String>) -> Self {
        self.props.title = Some(title.into());
        self
    }

    pub fn borders(mut self, borders: Borders) -> Self {
        self.props.borders = Some(borders);
        self
    }

    pub fn border_style(mut self, style: Style) -> Self {
        self.props.border_style = Some(style);
        self
    }

    pub fn children(mut self, children: Vec<Element>) -> Self {
        self.children = children;
        self
    }

    pub fn child(mut self, child: Element) -> Self {
        self.children.push(child);
        self
    }

    pub fn build(self) -> Element {
        VirtualNode::widget(WidgetType::Block, self.props, self.children)
    }
}

impl Default for BlockBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Builder for Layout widgets
pub struct LayoutBuilder {
    props: LayoutProps,
    children: Vec<Element>,
}

impl LayoutBuilder {
    pub fn new() -> Self {
        Self {
            props: LayoutProps::default(),
            children: vec![],
        }
    }

    pub fn direction(mut self, direction: Direction) -> Self {
        self.props.direction = Some(direction);
        self
    }

    pub fn constraints(mut self, constraints: Vec<Constraint>) -> Self {
        self.props.constraints = Some(constraints);
        self
    }

    pub fn margin(mut self, margin: u16) -> Self {
        self.props.margin = Some(margin);
        self
    }

    pub fn children(mut self, children: Vec<Element>) -> Self {
        self.children = children;
        self
    }

    pub fn child(mut self, child: Element) -> Self {
        self.children.push(child);
        self
    }

    pub fn build(self) -> Element {
        VirtualNode::widget(WidgetType::Layout, self.props, self.children)
    }
}

impl Default for LayoutBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// Render a virtual node to a ratatui frame
pub fn render_virtual_node(node: &VirtualNode, area: Rect, frame: &mut ratatui::Frame) {
    render_virtual_node_with_modals(node, area, frame, &mut Vec::new());
}

/// Render a virtual node to a ratatui frame, collecting modals for later rendering
fn render_virtual_node_with_modals(
    node: &VirtualNode,
    area: Rect,
    frame: &mut ratatui::Frame,
    modals: &mut Vec<VirtualNode>,
) {
    match node {
        VirtualNode::Text(content) => {
            let paragraph = Paragraph::new(content.as_str());
            paragraph.render(area, frame.buffer_mut());
        }
        VirtualNode::Widget {
            widget_type,
            props,
            children,
        } => match widget_type {
            WidgetType::Block => render_block_with_modals(props, children, area, frame, modals),
            WidgetType::Text => render_text_widget(props, area, frame),
            WidgetType::Layout => render_layout_with_modals(props, children, area, frame, modals),
            WidgetType::Modal => {
                // Collect modal for later rendering over everything else
                modals.push(node.clone());
            }
        },
        VirtualNode::Component {
            render_fn,
            props,
            name,
            ..
        } => {
            if name == "Dialog" {
                // Collect Dialog components for later rendering as modals
                let full_area = frame.area();
                let rendered = render_fn(props, Some(frame), Some(full_area));
                modals.push(rendered);
            } else {
                // Render regular components normally
                let rendered = render_fn(props, Some(frame), Some(area));
                render_virtual_node_with_modals(&rendered, area, frame, modals);
            }
        }
    }

    // After rendering the main content, render all collected modals over the entire screen
    if !modals.is_empty() {
        let full_area = frame.area();
        for modal in modals.drain(..) {
            render_modal_node(&modal, full_area, frame);
        }
    }
}

/// Render a modal node directly
fn render_modal_node(node: &VirtualNode, area: Rect, frame: &mut ratatui::Frame) {
    match node {
        VirtualNode::Widget {
            widget_type: WidgetType::Modal,
            props,
            children,
        } => render_modal(props, children, area, frame),
        _ => render_virtual_node(node, area, frame),
    }
}

fn render_block_with_modals(
    props: &TypedProps,
    children: &[VirtualNode],
    area: Rect,
    frame: &mut ratatui::Frame,
    modals: &mut Vec<VirtualNode>,
) {
    let mut block = Block::default();

    if let Some(block_props) = props.get::<BlockProps>() {
        if let Some(title) = &block_props.title {
            block = block.title(title.as_str());
        }

        if let Some(borders) = block_props.borders {
            block = block.borders(borders);
        }

        if let Some(style) = block_props.border_style {
            block = block.border_style(style);
        }
    }

    let inner_area = block.inner(area);
    block.render(area, frame.buffer_mut());

    // Render children in the inner area
    for child in children {
        render_virtual_node_with_modals(child, inner_area, frame, modals);
    }
}

fn render_text_widget(props: &TypedProps, area: Rect, frame: &mut ratatui::Frame) {
    if let Some(text_props) = props.get::<TextProps>() {
        let paragraph = Paragraph::new(text_props.content.as_str());
        paragraph.render(area, frame.buffer_mut());
    }
}

/// Check if a virtual node is a modal node (should render as overlay)
fn is_modal_node(node: &VirtualNode) -> bool {
    match node {
        VirtualNode::Widget { widget_type, .. } => matches!(widget_type, WidgetType::Modal),
        VirtualNode::Component { name, .. } => {
            // Check if this is a Dialog component
            name == "Dialog"
        }
        _ => false,
    }
}

fn render_layout_with_modals(
    props: &TypedProps,
    children: &[VirtualNode],
    area: Rect,
    frame: &mut ratatui::Frame,
    modals: &mut Vec<VirtualNode>,
) {
    let (direction, constraints) = if let Some(layout_props) = props.get::<LayoutProps>() {
        let direction = layout_props.direction.unwrap_or(Direction::Vertical);
        let constraints = layout_props
            .constraints
            .clone()
            .unwrap_or_else(|| vec![Constraint::Min(0); children.len()]);
        (direction, constraints)
    } else {
        (
            Direction::Vertical,
            vec![Constraint::Min(0); children.len()],
        )
    };

    // Separate modal children from regular children
    let (modal_children, regular_children): (Vec<_>, Vec<_>) =
        children.iter().partition(|child| is_modal_node(child));

    // Collect modal children for later rendering
    for modal_child in modal_children {
        modals.push(modal_child.clone());
    }

    // Only create layout for non-modal children
    if !regular_children.is_empty() {
        let layout = Layout::default()
            .direction(direction)
            .constraints(constraints);

        let chunks = layout.split(area);

        for (child, chunk) in regular_children.iter().zip(chunks.iter()) {
            render_virtual_node_with_modals(child, *chunk, frame, modals);
        }
    }
}

fn render_modal(
    props: &TypedProps,
    children: &[VirtualNode],
    area: Rect,
    frame: &mut ratatui::Frame,
) {
    use crate::ModalProps;
    use ratatui::style::Color;

    if let Some(modal_props) = props.get::<ModalProps>() {
        // Only render if modal is open
        if !modal_props.open {
            return;
        }

        // Render backdrop if enabled (default true)
        let show_backdrop = modal_props.backdrop.unwrap_or(true);
        if show_backdrop {
            // Fill the entire area with a visible backdrop
            let backdrop_block = Block::default().style(Style::default().bg(Color::DarkGray));
            backdrop_block.render(area, frame.buffer_mut());
        }

        // Calculate modal area (centered by default)
        let center = modal_props.center.unwrap_or(true);
        let modal_area = if center {
            // Center the modal - use 80% width and 60% height
            let width = (area.width as f32 * 0.8) as u16;
            let height = (area.height as f32 * 0.6) as u16;
            let x = area.x + (area.width.saturating_sub(width)) / 2;
            let y = area.y + (area.height.saturating_sub(height)) / 2;

            Rect {
                x,
                y,
                width,
                height,
            }
        } else {
            area
        };

        // Clear the modal area completely to prevent bleed-through
        for y in modal_area.top()..modal_area.bottom() {
            for x in modal_area.left()..modal_area.right() {
                let cell = frame.buffer_mut().cell_mut((x, y)).unwrap();
                cell.reset();
                cell.set_bg(Color::Black);
            }
        }

        // Create a border around the entire modal area for better UX
        let modal_block = Block::default()
            .borders(Borders::ALL)
            .border_style(Style::default().fg(Color::White))
            .style(Style::default().bg(Color::Black));

        let inner_area = modal_block.inner(modal_area);
        modal_block.render(modal_area, frame.buffer_mut());

        // Render modal content in the inner area with proper layout
        // If we have multiple children (like DialogTitle + DialogContent),
        // create a vertical layout for them with proper spacing
        if children.len() > 1 {
            // For DialogTitle + DialogContent, use fixed constraints
            // Title gets 1 line, content gets the rest
            let constraints = if children.len() == 2 {
                vec![
                    Constraint::Length(1), // Title: exactly 1 line
                    Constraint::Min(0),    // Content: all remaining space
                ]
            } else {
                // Fallback for other cases
                vec![Constraint::Percentage(100 / children.len() as u16); children.len()]
            };

            let layout = Layout::default()
                .direction(Direction::Vertical)
                .constraints(constraints);

            let chunks = layout.split(inner_area);

            // Render each child in its corresponding chunk
            for (child, chunk) in children.iter().zip(chunks.iter()) {
                render_virtual_node(child, *chunk, frame);
            }
        } else {
            // Single child - render in inner area with proper spacing
            let content_area = Rect {
                x: inner_area.x + 1,
                y: inner_area.y + 1,
                width: inner_area.width.saturating_sub(2),
                height: inner_area.height.saturating_sub(2),
            };

            for child in children {
                render_virtual_node(child, content_area, frame);
            }
        }
    }
}
