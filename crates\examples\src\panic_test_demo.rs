use terminus_ui::{prelude::*, setup_panic_handler};

/// Example demonstrating a panic inside a useEffect hook.
/// This is intended to test panic handling in effect execution.
#[derive(Pro<PERSON>, Debug, Clone)]
struct PanicEffectProps {
    pub title: String,
}

#[component(PanicEffect)]
fn panic_effect(props: PanicEffectProps) -> Element {
    // State to trigger re-renders
    let (counter, set_counter) = use_state(0);

    // Increment the counter on any key press
    if let Some(crossterm::event::Event::Key(key_event)) = use_event() {
        if key_event.is_press() {
            set_counter.call(counter.get() + 1);
        }
    }

    // Effect that panics when the counter changes
    let counter_value = counter.get();
    use_effect(
        move || {
            if counter_value > 0 {
                panic!(
                    "Intentional panic from useEffect! Counter = {}",
                    counter_value
                );
            }
            None::<Box<dyn FnOnce() + Send>>
        },
        counter_value,
    );

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Red)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("Counter: {}", counter_value)} />
                <Text content="Press any key to increment the counter and trigger a panic." />
            </Layout>
        </Block>
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚨 Starting useEffect Panic Demo...");

    // Initialize the panic handler
    setup_panic_handler();

    let app_props = PanicEffectProps {
        title: "💥 useEffect Panic Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <PanicEffect title={app_props.title} />
    };

    println!("🔴 This demo will panic when the counter is incremented.");
    println!("   - The panic is triggered inside a useEffect hook.");
    println!("   - Observe the panic handler output in your terminal.");

    // Run the UI
    render_async(element).await?;

    Ok(())
}
