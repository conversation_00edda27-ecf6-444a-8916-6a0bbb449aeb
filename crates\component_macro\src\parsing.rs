use crate::errors::{Component<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Compo<PERSON><PERSON><PERSON><PERSON>};
use syn::{Fn<PERSON>rg, <PERSON>, <PERSON>};

/// Information about component function parameters
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ComponentParams {
    pub props_param: Option<Type>,
    pub frame_param: Option<String>, // parameter name
    pub area_param: Option<String>,  // parameter name
}

/// Analyze component function parameters to detect props, frame, and area
///
/// This function uses AST pattern matching instead of string contains for robust type detection.
/// It properly handles:
/// - Generic types and complex type expressions
/// - Custom Props types (not just those ending in "Props")
/// - Proper parameter order validation
/// - Frame and Rect type detection with precise matching
pub fn analyze_component_params(
    inputs: &syn::punctuated::Punctuated<FnArg, syn::token::Comma>,
) -> ComponentResult<ComponentParams> {
    let mut props_param = None;
    let mut frame_param = None;
    let mut area_param = None;

    for arg in inputs.iter() {
        if let FnArg::Typed(pat_type) = arg {
            if let Pat::Ident(pat_ident) = &*pat_type.pat {
                let param_name = pat_ident.ident.to_string();

                // Analyze parameter type using AST pattern matching
                if is_frame_parameter(&param_name, &pat_type.ty)? {
                    if frame_param.is_some() {
                        return Err(ComponentMacroError::invalid_parameter_order(
                            pat_ident.ident.span(),
                            "Multiple frame parameters found. Only one frame parameter is allowed.",
                        ));
                    }
                    frame_param = Some(param_name);
                } else if is_area_parameter(&param_name, &pat_type.ty)? {
                    if area_param.is_some() {
                        return Err(ComponentMacroError::invalid_parameter_order(
                            pat_ident.ident.span(),
                            "Multiple area parameters found. Only one area parameter is allowed.",
                        ));
                    }
                    area_param = Some(param_name);
                } else {
                    // This is a props parameter - only set it if we haven't found one yet
                    if props_param.is_none() {
                        props_param = Some(pat_type.ty.as_ref().clone());
                    } else {
                        return Err(ComponentMacroError::invalid_parameter_order(
                            pat_ident.ident.span(),
                            "Multiple props parameters found. Only one props parameter is allowed, and it should be the first parameter.",
                        ));
                    }
                }
            }
        }
    }

    // Validate that we have at least a props parameter
    if props_param.is_none() && !inputs.is_empty() {
        return Err(ComponentMacroError::invalid_parameter_order(
            proc_macro2::Span::call_site(),
            "Component function must have at least one parameter for props.",
        ));
    }

    Ok(ComponentParams {
        props_param,
        frame_param,
        area_param,
    })
}

/// Check if a parameter is a frame parameter using AST pattern matching
///
/// Validates that:
/// - Parameter name is "frame"
/// - Type is &mut Frame or &mut terminus_ui::Frame or similar variants
fn is_frame_parameter(param_name: &str, param_type: &Type) -> ComponentResult<bool> {
    if param_name != "frame" {
        return Ok(false);
    }

    // Check if the type is a mutable reference
    if let Type::Reference(type_ref) = param_type {
        if type_ref.mutability.is_none() {
            return Err(ComponentMacroError::invalid_frame_parameter(
                proc_macro2::Span::call_site(),
                format!(
                    "Frame parameter must be mutable reference (&mut Frame), found immutable reference: {}",
                    quote::quote! { #param_type }
                ),
            ));
        }

        // Check if the referenced type is Frame
        if is_frame_type(&type_ref.elem) {
            return Ok(true);
        } else {
            return Err(ComponentMacroError::invalid_frame_parameter(
                proc_macro2::Span::call_site(),
                format!(
                    "Frame parameter must be &mut Frame, found: {}",
                    quote::quote! { #param_type }
                ),
            ));
        }
    }

    // If it's named "frame" but not a reference, it's an error
    Err(ComponentMacroError::invalid_frame_parameter(
        proc_macro2::Span::call_site(),
        format!(
            "Frame parameter must be &mut Frame, found: {}",
            quote::quote! { #param_type }
        ),
    ))
}

/// Check if a parameter is an area parameter using AST pattern matching
///
/// Validates that:
/// - Parameter name is "area"
/// - Type is Rect or terminus_ui::Rect or ratatui::layout::Rect
fn is_area_parameter(param_name: &str, param_type: &Type) -> ComponentResult<bool> {
    if param_name != "area" {
        return Ok(false);
    }

    // Check if the type is Rect
    if is_rect_type(param_type) {
        return Ok(true);
    }

    // If it's named "area" but not Rect type, it's an error
    Err(ComponentMacroError::invalid_area_parameter(
        proc_macro2::Span::call_site(),
        format!(
            "Area parameter must be Rect, found: {}",
            quote::quote! { #param_type }
        ),
    ))
}

/// Check if a type is Frame using AST pattern matching
///
/// Recognizes these patterns:
/// - Frame
/// - terminus_ui::Frame
/// - ratatui::Frame
/// - ::terminus_ui::Frame
/// - crate::Frame (if in terminus_ui context)
fn is_frame_type(ty: &Type) -> bool {
    match ty {
        Type::Path(type_path) => {
            let path = &type_path.path;

            // Check for simple "Frame"
            if path.segments.len() == 1 {
                return path.segments[0].ident == "Frame";
            }

            // Check for qualified paths like terminus_ui::Frame or ratatui::Frame
            if path.segments.len() >= 2 {
                let last_segment = &path.segments.last().unwrap().ident;
                return last_segment == "Frame";
            }

            false
        }
        _ => false,
    }
}

/// Check if a type is Rect using AST pattern matching
///
/// Recognizes these patterns:
/// - Rect
/// - terminus_ui::Rect
/// - ratatui::layout::Rect
/// - ::terminus_ui::Rect
/// - crate::Rect (if in terminus_ui context)
fn is_rect_type(ty: &Type) -> bool {
    match ty {
        Type::Path(type_path) => {
            let path = &type_path.path;

            // Check for simple "Rect"
            if path.segments.len() == 1 {
                return path.segments[0].ident == "Rect";
            }

            // Check for qualified paths like terminus_ui::Rect or ratatui::layout::Rect
            if path.segments.len() >= 2 {
                let last_segment = &path.segments.last().unwrap().ident;
                return last_segment == "Rect";
            }

            false
        }
        _ => false,
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use syn::parse_quote;

    #[test]
    fn test_frame_type_detection() {
        let frame_type: Type = parse_quote! { Frame };
        assert!(is_frame_type(&frame_type));

        let qualified_frame: Type = parse_quote! { terminus_ui::Frame };
        assert!(is_frame_type(&qualified_frame));

        let ratatui_frame: Type = parse_quote! { ratatui::Frame };
        assert!(is_frame_type(&ratatui_frame));

        let not_frame: Type = parse_quote! { String };
        assert!(!is_frame_type(&not_frame));
    }

    #[test]
    fn test_rect_type_detection() {
        let rect_type: Type = parse_quote! { Rect };
        assert!(is_rect_type(&rect_type));

        let qualified_rect: Type = parse_quote! { terminus_ui::Rect };
        assert!(is_rect_type(&qualified_rect));

        let layout_rect: Type = parse_quote! { ratatui::layout::Rect };
        assert!(is_rect_type(&layout_rect));

        let not_rect: Type = parse_quote! { String };
        assert!(!is_rect_type(&not_rect));
    }
}
