[package]
name = "terminus_ui_examples"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "simple"
path = "src/simple.rs"

[[bin]]
name = "layout_demo"
path = "src/layout_demo.rs"

[[bin]]
name = "nested_components"
path = "src/nested_components.rs"

[[bin]]
name = "react_like_demo"
path = "src/react_like_demo.rs"

[[bin]]
name = "validation_demo"
path = "src/validation_demo.rs"

[[bin]]
name = "missing_props_demo"
path = "src/missing_props_demo.rs"

[[bin]]
name = "frame_area_demo"
path = "src/frame_area_demo.rs"

[[bin]]
name = "async_demo"
path = "src/async_demo.rs"

[[bin]]
name = "use_state_demo"
path = "src/use_state_demo.rs"

[[bin]]
name = "use_effect_demo"
path = "src/use_effect_demo.rs"

[[bin]]
name = "use_current_event_demo"
path = "src/use_current_event_demo.rs"

[[bin]]
name = "use_context_demo"
path = "src/use_context_demo.rs"

[[bin]]
name = "panic_handler_demo"
path = "src/panic_handler_demo.rs"

[[bin]]
name = "panic_test_demo"
path = "src/panic_test_demo.rs"

[[bin]]
name = "panic_catch_demo"
path = "src/panic_catch_demo.rs"

[[bin]]
name = "use_future_demo"
path = "src/use_future_demo.rs"

[[bin]]
name = "callback_demo"
path = "src/callback_demo.rs"

[[bin]]
name = "use_event_comprehensive_demo"
path = "src/use_event_comprehensive_demo.rs"

[[bin]]
name = "use_event_patterns"
path = "src/use_event_patterns.rs"

[[bin]]
name = "enhanced_callback_demo"
path = "src/enhanced_callback_demo.rs"

[[bin]]
name = "children_demo"
path = "src/children_demo.rs"

[[bin]]
name = "modal_demo"
path = "src/modal_demo.rs"

[[bin]]
name = "simple_scroll"
path = "src/simple_scroll.rs"

[[bin]]
name = "scroll_area_demo"
path = "src/scroll_area_demo.rs"

[[bin]]
name = "comprehensive_scroll_demo"
path = "src/comprehensive_scroll_demo.rs"

[dependencies]
terminus_ui = { workspace = true }
tokio = { version = "1.0", features = ["full"] }
crossterm = { workspace = true }
