# ScrollArea Component

The ScrollArea component provides professional scrollable content functionality for terminal applications. It automatically manages scrollbars, handles user input, and provides smooth scrolling experiences.

## Features

- **Automatic Scrollbars**: Shows scrollbars only when content exceeds viewport
- **Dual Orientation**: Supports both horizontal and vertical scrolling
- **Customizable Appearance**: Full control over scrollbar symbols, colors, and positioning
- **Smooth Interactions**: Keyboard and mouse wheel support with configurable scroll speeds
- **React-like API**: Familiar component patterns following Shadcn UI design principles
- **Performance Optimized**: Efficient rendering for large content areas
- **Type Safe**: Full Rust type safety with comprehensive prop validation

## Basic Usage

```rust
use terminus_ui::prelude::*;

let element = rsx! {
    <ScrollArea>
        <Text content="Line 1: Scrollable content" />
        <Text content="Line 2: More content..." />
        <Text content="Line 3: Even more content..." />
    </ScrollArea>
};
```

## Props

### ScrollArea Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `Children` | Required | Content to be scrolled |
| `show_horizontal_scrollbar` | `Option<bool>` | `true` | Whether to show horizontal scrollbar |
| `show_vertical_scrollbar` | `Option<bool>` | `true` | Whether to show vertical scrollbar |
| `horizontal_scrollbar_position` | `Option<ScrollbarPosition>` | `Bottom` | Position of horizontal scrollbar |
| `vertical_scrollbar_position` | `Option<ScrollbarPosition>` | `Right` | Position of vertical scrollbar |
| `horizontal_scrollbar_appearance` | `Option<ScrollbarAppearance>` | Default | Custom horizontal scrollbar styling |
| `vertical_scrollbar_appearance` | `Option<ScrollbarAppearance>` | Default | Custom vertical scrollbar styling |

### ScrollBar Props (Standalone)

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `orientation` | `Option<ScrollOrientation>` | `Vertical` | Scrollbar orientation |
| `content_size` | `usize` | Required | Total content size |
| `viewport_size` | `usize` | Required | Visible viewport size |
| `scroll_offset` | `Option<usize>` | `0` | Current scroll position |
| `position` | `Option<ScrollbarPosition>` | Auto | Scrollbar position |
| `appearance` | `Option<ScrollbarAppearance>` | Default | Custom styling |

## Enums

### ScrollOrientation

```rust
pub enum ScrollOrientation {
    Vertical,   // Up/down scrolling
    Horizontal, // Left/right scrolling
}
```

### ScrollbarPosition

```rust
pub enum ScrollbarPosition {
    Top,    // For horizontal scrollbars
    Bottom, // For horizontal scrollbars
    Left,   // For vertical scrollbars
    Right,  // For vertical scrollbars
}
```

## Customization

### Custom Scrollbar Appearance

```rust
use terminus_ui::prelude::*;

let custom_appearance = ScrollbarAppearance {
    begin_symbol: "↑".to_string(),
    end_symbol: "↓".to_string(),
    track_symbol: "┃".to_string(),
    thumb_symbol: "█".to_string(),
    track_style: Some(Style::default().fg(Color::DarkGray)),
    thumb_style: Some(Style::default().fg(Color::White).bg(Color::Blue)),
    symbol_style: Some(Style::default().fg(Color::Cyan)),
};

let element = rsx! {
    <ScrollArea vertical_scrollbar_appearance={custom_appearance}>
        // Content here
    </ScrollArea>
};
```

### Scrollbar Positioning

```rust
// Vertical scrollbar on the left
let element = rsx! {
    <ScrollArea vertical_scrollbar_position={ScrollbarPosition::Left}>
        // Content here
    </ScrollArea>
};

// Horizontal scrollbar at the top
let element = rsx! {
    <ScrollArea horizontal_scrollbar_position={ScrollbarPosition::Top}>
        // Content here
    </ScrollArea>
};
```

### Disable Scrollbars

```rust
// Only vertical scrolling
let element = rsx! {
    <ScrollArea show_horizontal_scrollbar={false}>
        // Content here
    </ScrollArea>
};

// Only horizontal scrolling
let element = rsx! {
    <ScrollArea show_vertical_scrollbar={false}>
        // Content here
    </ScrollArea>
};
```

## Keyboard Controls

| Key | Action |
|-----|--------|
| `↑` | Scroll up one line |
| `↓` | Scroll down one line |
| `←` | Scroll left one column |
| `→` | Scroll right one column |
| `Page Up` | Scroll up one viewport |
| `Page Down` | Scroll down one viewport |
| `Home` | Jump to beginning |
| `End` | Jump to end |

## Mouse Controls

| Action | Effect |
|--------|--------|
| Scroll wheel up | Scroll up 3 lines |
| Scroll wheel down | Scroll down 3 lines |
| Scroll wheel left | Scroll left 3 columns |
| Scroll wheel right | Scroll right 3 columns |

## Advanced Usage

### Standalone ScrollBar

```rust
use terminus_ui::prelude::*;

let element = rsx! {
    <ScrollBar
        orientation={ScrollOrientation::Vertical}
        content_size={100}
        viewport_size={20}
        scroll_offset={10}
    />
};
```

### Complex Layout with Multiple ScrollAreas

```rust
use terminus_ui::prelude::*;

let element = rsx! {
    <Layout direction={Direction::Horizontal}>
        <ScrollArea>
            // Left panel content
        </ScrollArea>
        <ScrollArea>
            // Right panel content
        </ScrollArea>
    </Layout>
};
```

## Performance Considerations

- Content is rendered efficiently with viewport clipping
- Scrollbar calculations are optimized for smooth updates
- Event handling is debounced to prevent excessive re-renders
- Large content areas are handled gracefully

## Examples

See the following example files:
- `crates/examples/src/simple_scroll.rs` - Basic usage
- `crates/examples/src/scroll_area_demo.rs` - Comprehensive demo

Run examples with:
```bash
cargo run --example simple_scroll
cargo run --example scroll_area_demo
```
