//! ScrollArea and ScrollBar components for Terminus UI React-like interface
//!
//! This module provides scrollable content components that can be used with the rsx! macro
//! and follow the Shadcn UI component patterns with React-like architecture.

use crossterm::event::{Event, KeyCode, MouseEventKind};
use ratatui::{Frame, layout::Rect};
use terminus_ui_component_macro::component;
use terminus_ui_core::HasChildren;
use terminus_ui_core::TrySetChildren;
use terminus_ui_core::{
    Children, ComponentProps, Element, FunctionalComponent, PropFieldMetadata, PropRequirements,
    ScrollAreaProps, ScrollBarProps, ScrollOrientation, ScrollState, ScrollbarAppearance,
    ScrollbarPosition, VirtualNode, WidgetType, use_event, use_state,
};
use terminus_ui_props_macro::Props;

/// Props for the ScrollArea component
///
/// This follows the Shadcn UI component pattern:
/// ```rust
/// <ScrollArea>
///   <Text content="Long content that needs scrolling..." />
///   <Text content="More content..." />
/// </ScrollArea>
/// ```
#[derive(Props, Debug, Clone)]
pub struct ScrollAreaComponentProps {
    /// Child components to render inside the scrollable area
    #[children]
    pub children: Children,
    /// Whether to show horizontal scrollbar (default: true)
    pub show_horizontal_scrollbar: Option<bool>,
    /// Whether to show vertical scrollbar (default: true)
    pub show_vertical_scrollbar: Option<bool>,
    /// Position of horizontal scrollbar (default: Bottom)
    pub horizontal_scrollbar_position: Option<ScrollbarPosition>,
    /// Position of vertical scrollbar (default: Right)
    pub vertical_scrollbar_position: Option<ScrollbarPosition>,
    /// Custom appearance for horizontal scrollbar
    pub horizontal_scrollbar_appearance: Option<ScrollbarAppearance>,
    /// Custom appearance for vertical scrollbar
    pub vertical_scrollbar_appearance: Option<ScrollbarAppearance>,
}

/// Props for the ScrollBar component
///
/// This can be used standalone:
/// ```rust
/// <ScrollBar
///   orientation={ScrollOrientation::Vertical}
///   content_size={100}
///   viewport_size={20}
///   scroll_offset={10}
/// />
/// ```
#[derive(Props, Debug, Clone)]
pub struct ScrollBarComponentProps {
    /// Scroll orientation (default: Vertical)
    pub orientation: Option<ScrollOrientation>,
    /// Total content size
    pub content_size: usize,
    /// Visible viewport size
    pub viewport_size: usize,
    /// Current scroll offset (default: 0)
    pub scroll_offset: Option<usize>,
    /// Scrollbar position (default: Right for vertical, Bottom for horizontal)
    pub position: Option<ScrollbarPosition>,
    /// Custom appearance configuration
    pub appearance: Option<ScrollbarAppearance>,
}

/// Main ScrollArea component that provides scrollable content with automatic scrollbars
///
/// This component manages scroll state internally and handles keyboard/mouse events.
/// It automatically shows scrollbars when content exceeds the viewport size.
#[component(ScrollArea)]
pub fn scroll_area(props: ScrollAreaComponentProps) -> Element {
    // Initialize scroll states
    let (vertical_scroll_state, set_vertical_scroll_state) = use_state(ScrollState::default());
    let (horizontal_scroll_state, set_horizontal_scroll_state) = use_state(ScrollState::default());

    // Handle keyboard events for scrolling
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Up => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_by(-1);
                set_vertical_scroll_state.set(new_state);
            }
            KeyCode::Down => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_by(1);
                set_vertical_scroll_state.set(new_state);
            }
            KeyCode::Left => {
                let mut new_state = horizontal_scroll_state.get();
                new_state.scroll_by(-1);
                set_horizontal_scroll_state.set(new_state);
            }
            KeyCode::Right => {
                let mut new_state = horizontal_scroll_state.get();
                new_state.scroll_by(1);
                set_horizontal_scroll_state.set(new_state);
            }
            KeyCode::PageUp => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_by(-(new_state.viewport_size as isize));
                set_vertical_scroll_state.set(new_state);
            }
            KeyCode::PageDown => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_by(new_state.viewport_size as isize);
                set_vertical_scroll_state.set(new_state);
            }
            KeyCode::Home => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_to_start();
                set_vertical_scroll_state.set(new_state);
            }
            KeyCode::End => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_to_end();
                set_vertical_scroll_state.set(new_state);
            }
            _ => {}
        }
    }

    // Handle mouse events for scrolling
    if let Some(Event::Mouse(mouse)) = use_event() {
        match mouse.kind {
            MouseEventKind::ScrollUp => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_by(-3); // Scroll 3 lines at a time
                set_vertical_scroll_state.set(new_state);
            }
            MouseEventKind::ScrollDown => {
                let mut new_state = vertical_scroll_state.get();
                new_state.scroll_by(3); // Scroll 3 lines at a time
                set_vertical_scroll_state.set(new_state);
            }
            MouseEventKind::ScrollLeft => {
                let mut new_state = horizontal_scroll_state.get();
                new_state.scroll_by(-3);
                set_horizontal_scroll_state.set(new_state);
            }
            MouseEventKind::ScrollRight => {
                let mut new_state = horizontal_scroll_state.get();
                new_state.scroll_by(3);
                set_horizontal_scroll_state.set(new_state);
            }
            _ => {}
        }
    }

    // Build ScrollAreaProps for the widget
    let scroll_area_props = ScrollAreaProps {
        children: props.children.clone(),
        horizontal_scroll: if horizontal_scroll_state.get().needs_scrolling() {
            Some(horizontal_scroll_state.get())
        } else {
            None
        },
        vertical_scroll: if vertical_scroll_state.get().needs_scrolling() {
            Some(vertical_scroll_state.get())
        } else {
            None
        },
        show_horizontal_scrollbar: props.show_horizontal_scrollbar.unwrap_or(true),
        show_vertical_scrollbar: props.show_vertical_scrollbar.unwrap_or(true),
        horizontal_scrollbar_position: props
            .horizontal_scrollbar_position
            .unwrap_or(ScrollbarPosition::Bottom),
        vertical_scrollbar_position: props
            .vertical_scrollbar_position
            .unwrap_or(ScrollbarPosition::Right),
        horizontal_scrollbar_appearance: props
            .horizontal_scrollbar_appearance
            .unwrap_or_else(ScrollbarAppearance::horizontal),
        vertical_scrollbar_appearance: props
            .vertical_scrollbar_appearance
            .unwrap_or_else(ScrollbarAppearance::vertical),
        content_style: None,
    };

    VirtualNode::widget(
        WidgetType::ScrollArea,
        scroll_area_props,
        props.children.into_vec(),
    )
}

/// Standalone ScrollBar component
///
/// This component renders a scrollbar with the given configuration.
/// It's primarily used internally by ScrollArea but can also be used standalone.
#[component(ScrollBar)]
pub fn scroll_bar(props: ScrollBarComponentProps) -> Element {
    let orientation = props.orientation.unwrap_or(ScrollOrientation::Vertical);

    // Determine default position based on orientation
    let default_position = match orientation {
        ScrollOrientation::Vertical => ScrollbarPosition::Right,
        ScrollOrientation::Horizontal => ScrollbarPosition::Bottom,
    };

    let position = props.position.unwrap_or(default_position);

    // Create scroll state from props
    let scroll_state = ScrollState {
        offset: props.scroll_offset.unwrap_or(0),
        content_size: props.content_size,
        viewport_size: props.viewport_size,
    };

    // Determine default appearance based on orientation
    let default_appearance = match orientation {
        ScrollOrientation::Vertical => ScrollbarAppearance::vertical(),
        ScrollOrientation::Horizontal => ScrollbarAppearance::horizontal(),
    };

    let appearance = props.appearance.unwrap_or(default_appearance);

    let scrollbar_props = ScrollBarProps {
        orientation,
        scroll_state,
        position,
        appearance,
        size: None,
    };

    VirtualNode::widget(WidgetType::ScrollBar, scrollbar_props, vec![])
}

/// Helper function to calculate content dimensions for scroll state
/// This would typically be called during layout to determine actual content size
pub fn calculate_content_dimensions(children: &Children, viewport_rect: Rect) -> (usize, usize) {
    // For now, return placeholder values
    // In a real implementation, this would measure the actual rendered content
    let content_width = viewport_rect.width as usize + 20; // Simulate wider content
    let content_height = children.len() * 2; // Simulate taller content based on child count

    (content_width, content_height)
}

/// Helper function to update scroll state based on content and viewport dimensions
pub fn update_scroll_state_for_content(
    current_state: &ScrollState,
    content_size: usize,
    viewport_size: usize,
) -> ScrollState {
    let mut new_state = *current_state;
    new_state.content_size = content_size;
    new_state.viewport_size = viewport_size;

    // Ensure offset is still valid
    new_state.scroll_to(new_state.offset);

    new_state
}
