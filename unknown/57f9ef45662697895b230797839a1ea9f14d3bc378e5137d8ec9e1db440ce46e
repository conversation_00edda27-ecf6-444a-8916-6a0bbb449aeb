use crossterm::event::{Event, KeyC<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MouseEvent<PERSON>ind};
use terminus_ui::prelude::*;

/// Comprehensive demonstration of the use_event hook
/// This example shows practical patterns for handling terminal events

#[derive(<PERSON><PERSON>, Debu<PERSON>, <PERSON>lone)]
pub struct TextEditorProps {
    pub title: String,
}

/// A simple text editor component demonstrating keyboard event handling
#[component(TextEditor)]
fn text_editor(props: TextEditorProps) -> Element {
    let (content, set_content) = use_state(String::new());
    let (cursor_pos, set_cursor_pos) = use_state(0usize);
    let (status, set_status) = use_state("Ready".to_string());

    // Handle keyboard events for text editing
    if let Some(Event::Key(key)) = use_event() {
        if key.is_press() {
            let current_content = content.get();
            let current_cursor = cursor_pos.get();

            match key.code {
                KeyCode::Char(c) if !key.modifiers.contains(KeyModifiers::CONTROL) => {
                    // Insert character at cursor position
                    let mut new_content = current_content.clone();
                    new_content.insert(current_cursor, c);
                    set_content.set(new_content);
                    set_cursor_pos.set(current_cursor + 1);
                    set_status.set(format!("Inserted '{}'", c));
                }
                KeyCode::Backspace => {
                    // Delete character before cursor
                    if current_cursor > 0 {
                        let mut new_content = current_content.clone();
                        new_content.remove(current_cursor - 1);
                        set_content.set(new_content);
                        set_cursor_pos.set(current_cursor - 1);
                        set_status.set("Deleted character".to_string());
                    }
                }
                KeyCode::Left => {
                    // Move cursor left
                    if current_cursor > 0 {
                        set_cursor_pos.set(current_cursor - 1);
                        set_status.set("Moved left".to_string());
                    }
                }
                KeyCode::Right => {
                    // Move cursor right
                    if current_cursor < current_content.len() {
                        set_cursor_pos.set(current_cursor + 1);
                        set_status.set("Moved right".to_string());
                    }
                }
                KeyCode::Enter => {
                    // Insert newline
                    let mut new_content = current_content.clone();
                    new_content.insert(current_cursor, '\n');
                    set_content.set(new_content);
                    set_cursor_pos.set(current_cursor + 1);
                    set_status.set("New line".to_string());
                }
                KeyCode::Char('s') if key.modifiers.contains(KeyModifiers::CONTROL) => {
                    // Ctrl+S to "save"
                    set_status.set("Saved!".to_string());
                }
                KeyCode::Char('a') if key.modifiers.contains(KeyModifiers::CONTROL) => {
                    // Ctrl+A to select all (move cursor to end)
                    set_cursor_pos.set(current_content.len());
                    set_status.set("Select all".to_string());
                }
                _ => {}
            }
        }
    }

    let current_content = content.get();
    let current_cursor = cursor_pos.get();
    let current_status = status.get();

    // Display content with cursor indicator
    let display_content = if current_content.is_empty() {
        "Type something...".to_string()
    } else {
        let mut display = current_content.clone();
        if current_cursor <= display.len() {
            display.insert(current_cursor, '|'); // Cursor indicator
        }
        display
    };

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Min(3),      // Content area
                    Constraint::Length(1),   // Status line
                    Constraint::Length(2),   // Help text
                ]}
            >
                <Text content={display_content} />
                <Text content={format!("Status: {} | Cursor: {}", current_status, current_cursor)} />
                <Text content="Ctrl+S: Save | Ctrl+A: Select All | Arrow keys: Move cursor" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct DrawingCanvasProps {
    pub title: String,
}

/// A drawing canvas component demonstrating mouse event handling
#[component(DrawingCanvas)]
fn drawing_canvas(props: DrawingCanvasProps) -> Element {
    let (pixels, set_pixels) = use_state(Vec::<(u16, u16)>::new());
    let (is_drawing, set_is_drawing) = use_state(false);
    let (_last_pos, set_last_pos) = use_state((0u16, 0u16));
    let (tool_info, set_tool_info) = use_state("Click and drag to draw".to_string());

    // Handle mouse events for drawing
    if let Some(Event::Mouse(mouse)) = use_event() {
        let current_pixels = pixels.get();
        let current_drawing = is_drawing.get();

        match mouse.kind {
            MouseEventKind::Down(MouseButton::Left) => {
                // Start drawing
                set_is_drawing.set(true);
                let mut new_pixels = current_pixels.clone();
                new_pixels.push((mouse.column, mouse.row));
                set_pixels.set(new_pixels);
                set_last_pos.set((mouse.column, mouse.row));
                set_tool_info.set(format!("Drawing at ({}, {})", mouse.column, mouse.row));
            }
            MouseEventKind::Up(MouseButton::Left) => {
                // Stop drawing
                set_is_drawing.set(false);
                set_tool_info.set("Released mouse".to_string());
            }
            MouseEventKind::Drag(MouseButton::Left) if current_drawing => {
                // Continue drawing while dragging
                let mut new_pixels = current_pixels.clone();
                new_pixels.push((mouse.column, mouse.row));
                set_pixels.set(new_pixels);
                set_last_pos.set((mouse.column, mouse.row));
                set_tool_info.set(format!("Dragging to ({}, {})", mouse.column, mouse.row));
            }
            MouseEventKind::Down(MouseButton::Right) => {
                // Clear canvas on right click
                set_pixels.set(Vec::new());
                set_is_drawing.set(false);
                set_tool_info.set("Canvas cleared".to_string());
            }
            MouseEventKind::Moved => {
                // Show mouse position when moving
                if !current_drawing {
                    set_tool_info.set(format!("Mouse at ({}, {})", mouse.column, mouse.row));
                }
            }
            _ => {}
        }
    }

    let current_pixels = pixels.get();
    let current_tool_info = tool_info.get();
    let pixel_count = current_pixels.len();

    // Create a simple visualization of drawn pixels
    let canvas_display = if current_pixels.is_empty() {
        "Empty canvas - click and drag to draw!".to_string()
    } else {
        format!("Canvas with {} pixels drawn", pixel_count)
    };

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Min(3),      // Canvas area
                    Constraint::Length(1),   // Tool info
                    Constraint::Length(2),   // Help text
                ]}
            >
                <Text content={canvas_display} />
                <Text content={format!("Tool: {}", current_tool_info)} />
                <Text content="Left click/drag: Draw | Right click: Clear | Move: Show position" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct WindowManagerProps {
    pub title: String,
}

/// A window manager component demonstrating resize event handling
#[component(WindowManager)]
fn window_manager(props: WindowManagerProps) -> Element {
    let (window_size, set_window_size) = use_state((80u16, 24u16));
    let (resize_count, set_resize_count) = use_state(0u32);
    let (size_category, set_size_category) = use_state("Medium".to_string());

    // Handle resize events
    if let Some(Event::Resize(width, height)) = use_event() {
        set_window_size.set((width, height));
        set_resize_count.update(|prev| prev + 1);

        // Categorize window size
        let category = match (width, height) {
            (w, h) if w < 60 || h < 15 => "Small",
            (w, h) if w > 120 || h > 40 => "Large",
            _ => "Medium",
        };
        set_size_category.set(category.to_string());
    }

    let (current_width, current_height) = window_size.get();
    let current_resize_count = resize_count.get();
    let current_category = size_category.get();

    let area = current_width as u32 * current_height as u32;
    let aspect_ratio = current_width as f32 / current_height as f32;

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),   // Size info
                    Constraint::Length(1),   // Area info
                    Constraint::Length(1),   // Aspect ratio
                    Constraint::Length(1),   // Category
                    Constraint::Length(1),   // Resize count
                    Constraint::Min(1),      // Help text
                ]}
            >
                <Text content={format!("Size: {}x{}", current_width, current_height)} />
                <Text content={format!("Area: {} characters", area)} />
                <Text content={format!("Aspect Ratio: {:.2}", aspect_ratio)} />
                <Text content={format!("Category: {}", current_category)} />
                <Text content={format!("Resizes: {}", current_resize_count)} />
                <Text content="Resize your terminal window to see live updates!" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct EventDemoAppProps {
    pub title: String,
}

/// Main application demonstrating comprehensive use_event patterns
#[component(EventDemoApp)]
fn event_demo_app(props: EventDemoAppProps) -> Element {
    let (global_event_count, set_global_event_count) = use_state(0u32);

    // Global event counter - this hook will catch any event
    if let Some(_event) = use_event() {
        set_global_event_count.update(|prev| prev + 1);
    }

    let current_count = global_event_count.get();

    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(1),
                        Constraint::Length(1),
                    ]}
                >
                    <Text content="Comprehensive use_event Hook Demonstration" />
                    <Text content={format!("Global Event Count: {}", current_count)} />
                </Layout>
            </Block>

            // Main content area
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),  // Left column
                    Constraint::Percentage(50),  // Right column
                ]}
            >
                // Left column
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Percentage(60),  // Text editor
                        Constraint::Percentage(40),  // Drawing canvas
                    ]}
                >
                    <TextEditor title={"📝 Text Editor (Keyboard Events)".to_string()} />
                    <DrawingCanvas title={"🎨 Drawing Canvas (Mouse Events)".to_string()} />
                </Layout>

                // Right column
                <WindowManager title={"🪟 Window Manager (Resize Events)".to_string()} />
            </Layout>

            // Footer
            <Block
                title={"Instructions".to_string()}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Try typing in the editor, drawing with mouse, and resizing the terminal. Press 'q' to quit." />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_props = EventDemoAppProps {
        title: "🎮 Comprehensive use_event Hook Demo".to_string(),
    };

    let element = rsx! {
        <EventDemoApp title={app_props.title} />
    };

    render(element)
}
