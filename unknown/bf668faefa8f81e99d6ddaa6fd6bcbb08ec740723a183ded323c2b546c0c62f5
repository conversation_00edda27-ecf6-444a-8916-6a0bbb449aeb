use crossterm::event::{Event, Key<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MouseEvent<PERSON>ind};
use terminus_ui::prelude::*;

/// Demonstrates essential use_event hook patterns
/// This example shows the most common and useful patterns for event handling

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct KeyCounterProps {
    pub title: String,
}

/// Pattern 1: Basic keyboard event handling with state updates
#[component(KeyCounter)]
fn key_counter(props: KeyCounterProps) -> Element {
    let (key_count, set_key_count) = use_state(0u32);
    let (last_key, set_last_key) = use_state("None".to_string());

    // Basic pattern: Check for keyboard events and update state
    if let Some(Event::Key(key)) = use_event() {
        if !key.is_press() {
            set_key_count.update(|prev| prev + 1);

            let key_description = match key.code {
                KeyCode::Char(c) => format!("'{}'", c),
                KeyCode::Enter => "Enter".to_string(),
                KeyCode::Backspace => "Backspace".to_string(),
                KeyCode::Tab => "Tab".to_string(),
                KeyCode::Esc => "Escape".to_string(),
                KeyCode::Up => "↑".to_string(),
                KeyCode::Down => "↓".to_string(),
                KeyCode::Left => "←".to_string(),
                KeyCode::Right => "→".to_string(),
                _ => format!("{:?}", key.code),
            };

            // Add modifier information
            let modifiers = if key.modifiers.is_empty() {
                String::new()
            } else {
                let mut mods = Vec::new();
                if key.modifiers.contains(KeyModifiers::CONTROL) {
                    mods.push("Ctrl");
                }
                if key.modifiers.contains(KeyModifiers::ALT) {
                    mods.push("Alt");
                }
                if key.modifiers.contains(KeyModifiers::SHIFT) {
                    mods.push("Shift");
                }
                format!("{}+", mods.join("+"))
            };

            set_last_key.set(format!("{}{}", modifiers, key_description));
        }
    }

    let current_count = key_count.get();
    let current_last_key = last_key.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Min(1),
                ]}
            >
                <Text content={format!("Keys pressed: {}", current_count)} />
                <Text content={format!("Last key: {}", current_last_key)} />
                <Text content="Type any key to see it counted!" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct MouseTrackerProps {
    pub title: String,
}

/// Pattern 2: Mouse event handling with position tracking
#[component(MouseTracker)]
fn mouse_tracker(props: MouseTrackerProps) -> Element {
    let (mouse_pos, set_mouse_pos) = use_state((0u16, 0u16));
    let (click_count, set_click_count) = use_state(0u32);
    let (last_action, set_last_action) = use_state("None".to_string());

    // Pattern: Handle different mouse event types
    if let Some(Event::Mouse(mouse)) = use_event() {
        set_mouse_pos.set((mouse.column, mouse.row));

        match mouse.kind {
            MouseEventKind::Down(MouseButton::Left) => {
                set_click_count.update(|prev| prev + 1);
                set_last_action.set("Left Click".to_string());
            }
            MouseEventKind::Down(MouseButton::Right) => {
                set_last_action.set("Right Click".to_string());
            }
            MouseEventKind::Down(MouseButton::Middle) => {
                set_last_action.set("Middle Click".to_string());
            }
            MouseEventKind::Up(_) => {
                set_last_action.set("Mouse Up".to_string());
            }
            MouseEventKind::Drag(_) => {
                set_last_action.set("Dragging".to_string());
            }
            MouseEventKind::Moved => {
                set_last_action.set("Moving".to_string());
            }
            MouseEventKind::ScrollDown => {
                set_last_action.set("Scroll Down".to_string());
            }
            MouseEventKind::ScrollUp => {
                set_last_action.set("Scroll Up".to_string());
            }
            MouseEventKind::ScrollLeft => {
                set_last_action.set("Scroll Left".to_string());
            }
            MouseEventKind::ScrollRight => {
                set_last_action.set("Scroll Right".to_string());
            }
        }
    }

    let (current_x, current_y) = mouse_pos.get();
    let current_clicks = click_count.get();
    let current_action = last_action.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Min(1),
                ]}
            >
                <Text content={format!("Mouse position: ({}, {})", current_x, current_y)} />
                <Text content={format!("Left clicks: {}", current_clicks)} />
                <Text content={format!("Last action: {}", current_action)} />
                <Text content="Move mouse and click to see tracking!" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct HotkeyHandlerProps {
    pub title: String,
}

/// Pattern 3: Hotkey handling with specific key combinations
#[component(HotkeyHandler)]
fn hotkey_handler(props: HotkeyHandlerProps) -> Element {
    let (hotkey_count, set_hotkey_count) = use_state(0u32);
    let (last_hotkey, set_last_hotkey) = use_state("None".to_string());
    let (mode, set_mode) = use_state("Normal".to_string());

    // Pattern: Handle specific key combinations
    if let Some(Event::Key(key)) = use_event() {
        match (key.code, key.modifiers) {
            // Ctrl+S - Save hotkey
            (KeyCode::Char('s'), mods) if mods.contains(KeyModifiers::CONTROL) => {
                set_hotkey_count.update(|prev| prev + 1);
                set_last_hotkey.set("Ctrl+S (Save)".to_string());
            }
            // Ctrl+C - Copy hotkey
            (KeyCode::Char('c'), mods) if mods.contains(KeyModifiers::CONTROL) => {
                set_hotkey_count.update(|prev| prev + 1);
                set_last_hotkey.set("Ctrl+C (Copy)".to_string());
            }
            // Ctrl+V - Paste hotkey
            (KeyCode::Char('v'), mods) if mods.contains(KeyModifiers::CONTROL) => {
                set_hotkey_count.update(|prev| prev + 1);
                set_last_hotkey.set("Ctrl+V (Paste)".to_string());
            }
            // Alt+Tab - Switch mode
            (KeyCode::Tab, mods) if mods.contains(KeyModifiers::ALT) => {
                let new_mode = if mode.get() == "Normal" {
                    "Edit"
                } else {
                    "Normal"
                };
                set_mode.set(new_mode.to_string());
                set_last_hotkey.set("Alt+Tab (Mode Switch)".to_string());
            }
            // F1-F12 function keys
            (KeyCode::F(n), _) => {
                set_hotkey_count.update(|prev| prev + 1);
                set_last_hotkey.set(format!("F{} (Function Key)", n));
            }
            _ => {} // Ignore other keys
        }
    }

    let current_count = hotkey_count.get();
    let current_hotkey = last_hotkey.get();
    let current_mode = mode.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Length(1),
                    Constraint::Min(2),
                ]}
            >
                <Text content={format!("Hotkeys pressed: {}", current_count)} />
                <Text content={format!("Last hotkey: {}", current_hotkey)} />
                <Text content={format!("Current mode: {}", current_mode)} />
                <Text content="Try: Ctrl+S, Ctrl+C, Ctrl+V, Alt+Tab, F1-F12" />
            </Layout>
        </Block>
    }
}

#[derive(Props, Debug, Clone)]
pub struct EventPatternsAppProps {
    pub title: String,
}

/// Main application demonstrating use_event patterns
#[component(EventPatternsApp)]
fn event_patterns_app(props: EventPatternsAppProps) -> Element {
    let (total_events, set_total_events) = use_state(0u32);

    // Pattern: Global event counter that catches all events
    if use_event().is_some() {
        set_total_events.update(|prev| prev + 1);
    }

    let current_total = total_events.get();

    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(1),
                        Constraint::Length(1),
                    ]}
                >
                    <Text content="Essential use_event Hook Patterns" />
                    <Text content={format!("Total events processed: {}", current_total)} />
                </Layout>
            </Block>

            // Main content
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(33),
                    Constraint::Percentage(33),
                    Constraint::Percentage(34),
                ]}
            >
                <KeyCounter title={"⌨️ Pattern 1: Key Events".to_string()} />
                <MouseTracker title={"🖱️ Pattern 2: Mouse Events".to_string()} />
                <HotkeyHandler title={"🔥 Pattern 3: Hotkeys".to_string()} />
            </Layout>

            // Footer
            <Block
                title={"Key Concepts".to_string()}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="• Each use_event() call gets the event once per render • Multiple components can use use_event() independently • Events are automatically cleared between renders" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_props = EventPatternsAppProps {
        title: "🎯 Essential use_event Patterns".to_string(),
    };

    let element = rsx! {
        <EventPatternsApp title={app_props.title} />
    };

    render(element)
}
