use crate::VirtualNode;

/// Represents a change that needs to be applied to the UI
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum Change {
    /// Insert a new node at the given path
    Insert { path: Vec<usize>, node: VirtualNode },
    /// Update an existing node at the given path
    Update { path: Vec<usize>, node: VirtualNode },
    /// Remove a node at the given path
    Remove { path: Vec<usize> },
    /// Replace a node at the given path
    Replace { path: Vec<usize>, node: VirtualNode },
}

/// The reconciler is responsible for diffing virtual DOM trees and producing changes
pub struct Reconciler {
    /// The current virtual DOM tree
    current_tree: Option<VirtualNode>,
}

impl Reconciler {
    pub fn new() -> Self {
        Self { current_tree: None }
    }

    /// Reconcile a new tree with the current tree and return the changes
    pub fn reconcile(&mut self, new_tree: VirtualNode) -> Vec<Change> {
        let changes = match &self.current_tree {
            None => {
                // First render - everything is new
                vec![Change::Insert {
                    path: vec![],
                    node: new_tree.clone(),
                }]
            }
            Some(current) => {
                // Diff the trees
                self.diff_nodes(current, &new_tree, vec![])
            }
        };

        self.current_tree = Some(new_tree);
        changes
    }

    /// Diff two virtual nodes and return the changes
    fn diff_nodes(&self, old: &VirtualNode, new: &VirtualNode, path: Vec<usize>) -> Vec<Change> {
        let mut changes = Vec::new();

        match (old, new) {
            // Both are text nodes
            (VirtualNode::Text(old_text), VirtualNode::Text(new_text)) => {
                if old_text != new_text {
                    changes.push(Change::Replace {
                        path,
                        node: new.clone(),
                    });
                }
            }

            // Both are widgets of the same type
            (
                VirtualNode::Widget {
                    widget_type: old_type,
                    props: old_props,
                    children: old_children,
                },
                VirtualNode::Widget {
                    widget_type: new_type,
                    props: new_props,
                    children: new_children,
                },
            ) => {
                if old_type != new_type {
                    // Different widget types - replace entirely
                    changes.push(Change::Replace {
                        path,
                        node: new.clone(),
                    });
                } else {
                    // Same widget type - check props and children
                    if !self.props_equal(old_props, new_props) {
                        changes.push(Change::Update {
                            path: path.clone(),
                            node: new.clone(),
                        });
                    }

                    // Diff children
                    changes.extend(self.diff_children(old_children, new_children, path));
                }
            }

            // Both are components
            (
                VirtualNode::Component {
                    name: old_name,
                    props: old_props,
                    children: old_children,
                    ..
                },
                VirtualNode::Component {
                    name: new_name,
                    props: new_props,
                    children: new_children,
                    ..
                },
            ) => {
                if old_name != new_name {
                    // Different component types - replace entirely
                    changes.push(Change::Replace {
                        path,
                        node: new.clone(),
                    });
                } else {
                    // Same component type - check props and children
                    if !self.props_equal(old_props, new_props) {
                        changes.push(Change::Update {
                            path: path.clone(),
                            node: new.clone(),
                        });
                    }

                    // Diff children
                    changes.extend(self.diff_children(old_children, new_children, path));
                }
            }

            // Different node types - replace entirely
            _ => {
                changes.push(Change::Replace {
                    path,
                    node: new.clone(),
                });
            }
        }

        changes
    }

    /// Diff children arrays
    fn diff_children(
        &self,
        old_children: &[VirtualNode],
        new_children: &[VirtualNode],
        path: Vec<usize>,
    ) -> Vec<Change> {
        let mut changes = Vec::new();

        let old_len = old_children.len();
        let new_len = new_children.len();
        let min_len = old_len.min(new_len);

        // Diff existing children
        for i in 0..min_len {
            let mut child_path = path.clone();
            child_path.push(i);
            changes.extend(self.diff_nodes(&old_children[i], &new_children[i], child_path));
        }

        // Handle length differences
        if new_len > old_len {
            // New children added
            for (i, node) in new_children.iter().enumerate().skip(old_len) {
                let mut child_path = path.clone();
                child_path.push(i);
                changes.push(Change::Insert {
                    path: child_path,
                    node: node.clone(),
                });
            }
        } else if old_len > new_len {
            // Children removed
            for i in (new_len..old_len).rev() {
                let mut child_path = path.clone();
                child_path.push(i);
                changes.push(Change::Remove { path: child_path });
            }
        }

        changes
    }

    /// Compare two props objects for equality
    /// This is a simplified comparison - in a real implementation,
    /// we'd need more sophisticated prop comparison
    fn props_equal(&self, _old_props: &crate::TypedProps, _new_props: &crate::TypedProps) -> bool {
        // For now, assume props are always different if we're comparing them
        // TODO: Implement proper prop comparison
        false
    }

    /// Get the current tree
    pub fn current_tree(&self) -> Option<&VirtualNode> {
        self.current_tree.as_ref()
    }
}

impl Default for Reconciler {
    fn default() -> Self {
        Self::new()
    }
}

/// Apply changes to a virtual DOM tree
pub fn apply_changes(tree: &mut Option<VirtualNode>, changes: Vec<Change>) {
    for change in changes {
        apply_change(tree, change);
    }
}

/// Apply a single change to a virtual DOM tree
fn apply_change(tree: &mut Option<VirtualNode>, change: Change) {
    match change {
        Change::Insert { path, node } => {
            if path.is_empty() {
                *tree = Some(node);
            } else {
                // TODO: Implement insertion at specific paths
            }
        }
        Change::Update { path, node } => {
            if path.is_empty() {
                *tree = Some(node);
            } else {
                // TODO: Implement updates at specific paths
            }
        }
        Change::Remove { path } => {
            if path.is_empty() {
                *tree = None;
            } else {
                // TODO: Implement removal at specific paths
            }
        }
        Change::Replace { path, node } => {
            if path.is_empty() {
                *tree = Some(node);
            } else {
                // TODO: Implement replacement at specific paths
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_reconciler_first_render() {
        let mut reconciler = Reconciler::new();
        let tree = VirtualNode::text("Hello");

        let changes = reconciler.reconcile(tree.clone());
        assert_eq!(changes.len(), 1);

        match &changes[0] {
            Change::Insert { path, node } => {
                assert!(path.is_empty());
                assert!(matches!(node, VirtualNode::Text(_)));
            }
            _ => panic!("Expected Insert change"),
        }
    }

    #[test]
    fn test_reconciler_text_change() {
        let mut reconciler = Reconciler::new();

        // First render
        let tree1 = VirtualNode::text("Hello");
        reconciler.reconcile(tree1);

        // Second render with different text
        let tree2 = VirtualNode::text("World");
        let changes = reconciler.reconcile(tree2);

        assert_eq!(changes.len(), 1);
        match &changes[0] {
            Change::Replace { path, node } => {
                assert!(path.is_empty());
                assert!(matches!(node, VirtualNode::Text(_)));
            }
            _ => panic!("Expected Replace change"),
        }
    }
}
