use std::any::Any;
use std::io::{self, Write};
use std::panic;
use std::sync::Once;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tracing::{error, info};
use tracing_appender::non_blocking::WorkerGuard;
use tracing_subscriber::Registry;
use tracing_subscriber::{
    filter::{EnvFilter, LevelFilter},
    fmt,
    prelude::*,
    util::SubscriberInitExt,
};

#[cfg(debug_assertions)]
use better_panic::{Settings, Verbosity};

#[cfg(not(debug_assertions))]
use human_panic::setup_panic;

static INIT: Once = Once::new();
static mut LOG_GUARD: Option<WorkerGuard> = None;

/// Sets up a custom panic hook for the application with advanced features.
///
/// This function configures panic behavior based on the build profile:
/// - **Debug builds**: Uses `better_panic` for verbose, immediate, and diagnostic-rich panics with full stack traces.
/// - **Release builds**: Uses `human_panic` for graceful, user-friendly panics that log internally without exposing sensitive details, prioritizing user experience.
///
/// Additionally, it provides a mechanism to catch panics from spawned Tokio tasks.
///
/// This function should be called only once. Subsequent calls will be ignored.
pub fn setup_panic_handler() {
    INIT.call_once(|| {
        // Initialize tracing subscriber for internal logging regardless of build type
        let env_filter = EnvFilter::from_default_env().add_directive(LevelFilter::INFO.into());
        let console_layer = fmt::Layer::new().with_writer(io::stderr);

        let subscriber = Registry::default().with(env_filter).with(console_layer);

        // For file logging, we can still use tracing-appender
        // This part is independent of debug/release panic behavior
        let log_file_path = "./logs".to_string(); // Example path, could be configurable
        let file_appender = tracing_appender::rolling::daily(log_file_path, "application.log");
        let (non_blocking_appender, guard) = tracing_appender::non_blocking(file_appender);
        unsafe {
            LOG_GUARD = Some(guard);
        }
        let file_layer = fmt::Layer::new().with_writer(non_blocking_appender).json();
        subscriber.with(file_layer).init();

        #[cfg(debug_assertions)]
        {
            // For debug builds, use better_panic for detailed output
            Settings::auto()
                .most_recent_first(false)
                .lineno_suffix(true)
                .verbosity(Verbosity::Full)
                .install();
            info!("Panic handler configured for DEBUG (better_panic).");
        }

        #[cfg(not(debug_assertions))]
        {
            // For release builds, use human_panic for user-friendly messages
            setup_panic!();
            info!("Panic handler configured for RELEASE (human_panic).");
        }

        // Custom panic hook to log to tracing system before the specific handler takes over
        let original_hook = panic::take_hook();
        panic::set_hook(Box::new(move |panic_info| {
            error!(
                target: "panic_handler",
                location = %panic_info.location().map_or("Unknown".to_string(), |l| format!("{}:{}:{}", l.file(), l.line(), l.column())),
                payload = %panic_info.payload().downcast_ref::<&str>().unwrap_or(&"<unknown>"),
                "Application panicked"
            );
            // Call the original hook to ensure better_panic/human_panic are triggered
            original_hook(panic_info);
            let _ = io::stderr().flush();
        }));
    });
}

/// Spawns a new asynchronous task and catches any panics that occur within it.
///
/// If a panic occurs, it will be caught by the custom panic hook.
pub fn spawn_catch_panic<F>(future: F) -> JoinHandle<F::Output>
where
    F: std::future::Future + Send + 'static,
    F::Output: Send + 'static,
{
    tokio::spawn(async move {
        let result = panic::catch_unwind(std::panic::AssertUnwindSafe(|| future));
        match result {
            Ok(output_future) => output_future.await,
            Err(e) => {
                // Re-panic on the main thread to trigger the custom panic hook
                panic::resume_unwind(e);
            }
        }
    })
}

/// Executes a closure and catches any panics that occur, returning a Result.
///
/// # Example
/// ```
/// use terminus_ui_core::panic_handler::catch_panic;
///
/// let ok = catch_panic(|| 42);
/// assert_eq!(ok, Ok(42));
///
/// let err = catch_panic(|| panic!("fail!"));
/// assert!(err.is_err());
/// ```
pub fn catch_panic<T, F>(f: F) -> Result<T, Box<dyn Any + Send + 'static>>
where
    F: FnOnce() -> T + std::panic::UnwindSafe,
{
    std::panic::catch_unwind(f)
}
