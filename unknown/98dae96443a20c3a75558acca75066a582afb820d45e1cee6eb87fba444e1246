use proc_macro::TokenStream;
use quote::quote;
use syn::{Data, DeriveInput, Fields, Ident, parse_macro_input};

/// The #[derive(Props)] macro for creating type-safe component props
#[proc_macro_derive(Props, attributes(children))]
pub fn derive_props(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let output = generate_props_impl(&input);
    output.into()
}

/// The #[derive(ValidatedProps)] macro for creating validated component props
#[proc_macro_derive(ValidatedProps, attributes(validate))]
pub fn derive_validated_props(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let output = generate_validated_props_impl(&input);
    output.into()
}

/// Macro to generate compile-time error messages for missing props
#[proc_macro]
pub fn check_required_props(input: TokenStream) -> TokenStream {
    let input_str = input.to_string();

    // Parse the input to extract component name and provided props
    // Format: check_required_props!(ComponentName, [prop1, prop2, ...])
    if let Some((component_part, props_part)) = input_str.split_once(',') {
        let component_name = component_part.trim();
        let props_str = props_part.trim();

        // Extract prop names from the array syntax
        let props_str = props_str.trim_start_matches('[').trim_end_matches(']');
        let provided_props: Vec<&str> = if props_str.is_empty() {
            vec![]
        } else {
            props_str.split(',').map(|s| s.trim()).collect()
        };

        // Generate a compile-time check
        let _component_ident = syn::Ident::new(component_name, proc_macro2::Span::call_site());
        let props_type_name = format!("{}Props", component_name);
        let props_type = syn::Ident::new(&props_type_name, proc_macro2::Span::call_site());

        let provided_props_tokens: Vec<_> = provided_props
            .iter()
            .map(|prop| quote::quote! { #prop })
            .collect();

        let output = quote::quote! {
            {
                // Compile-time validation using const evaluation
                const _: () = {
                    // This will be evaluated at compile time
                    fn check_props<T: PropRequirements>() {
                        let required = T::required_fields();
                        let provided = &[#(#provided_props_tokens),*];

                        // Check each required field
                        let mut i = 0;
                        while i < required.len() {
                            let mut found = false;
                            let mut j = 0;
                            while j < provided.len() {
                                if required[i] == provided[j] {
                                    found = true;
                                    break;
                                }
                                j += 1;
                            }
                            if !found {
                                // This will cause a compile error with the field name
                                panic!("Missing required prop: {}", required[i]);
                            }
                            i += 1;
                        }
                    }
                    check_props::<#props_type>();
                };
            }
        };

        output.into()
    } else {
        // Invalid input format
        quote::quote! {
            compile_error!("Invalid format for check_required_props! Expected: check_required_props!(ComponentName, [prop1, prop2, ...])");
        }.into()
    }
}

/// Generate the Props trait implementation
fn generate_props_impl(input: &DeriveInput) -> proc_macro2::TokenStream {
    let struct_name = &input.ident;

    // Extract fields from the struct
    let fields = match &input.data {
        Data::Struct(data_struct) => match &data_struct.fields {
            Fields::Named(fields_named) => &fields_named.named,
            _ => {
                return quote! {
                    compile_error!("Props can only be derived for structs with named fields");
                };
            }
        },
        _ => {
            return quote! {
                compile_error!("Props can only be derived for structs");
            };
        }
    };

    // Generate field accessors
    let field_accessors = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;

        quote! {
            pub fn #field_name(&self) -> &#field_type {
                &self.#field_name
            }
        }
    });

    // Generate builder methods
    let builder_methods = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;
        let method_name = Ident::new(&format!("with_{}", field_name), field_name.span());

        quote! {
            pub fn #method_name(mut self, value: #field_type) -> Self {
                self.#field_name = value;
                self
            }
        }
    });

    // Check if any field has the #[children] attribute
    let children_field = fields
        .iter()
        .find(|field| has_children_attribute(&field.attrs));
    let has_children = children_field.is_some();

    // Generate field metadata
    let field_metadata_items = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;
        let field_name_str = field_name.to_string();
        let field_type_str = quote! { #field_type }.to_string();

        // Check if the field type is Option<T>
        let is_optional = is_option_type(field_type);

        // Check if this is a children field
        let _is_children = has_children_attribute(&field.attrs);

        quote! {
            PropFieldMetadata {
                name: #field_name_str.to_string(),
                type_name: #field_type_str.to_string(),
                is_optional: #is_optional,
                default_value: None, // TODO: Extract default values from attributes
            }
        }
    });

    // Generate PropRequirements implementation
    let required_fields: Vec<_> = fields
        .iter()
        .filter(|field| !is_option_type(&field.ty))
        .map(|field| {
            let field_name = field.ident.as_ref().unwrap().to_string();
            quote! { #field_name }
        })
        .collect();

    let all_fields: Vec<_> = fields
        .iter()
        .map(|field| {
            let field_name = field.ident.as_ref().unwrap().to_string();
            let field_type = &field.ty;
            let field_type_str = quote! { #field_type }.to_string();
            quote! { (#field_name, #field_type_str) }
        })
        .collect();

    // Generate Default implementation that handles optional fields intelligently
    let default_field_values = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;

        if is_option_type(field_type) {
            // Optional fields default to None
            quote! { #field_name: None }
        } else {
            // Required fields use Default::default() - this will fail if the type doesn't implement Default
            // This is intentional to catch cases where required fields don't have sensible defaults
            quote! { #field_name: Default::default() }
        }
    });

    // Generate HasChildren trait implementation if there's a children field
    let has_children_impl = if has_children {
        let children_field_name = children_field.unwrap().ident.as_ref().unwrap();
        quote! {
            impl HasChildren for #struct_name {
                fn children_field_name() -> &'static str {
                    stringify!(#children_field_name)
                }

                fn set_children(&mut self, children: Children) {
                    self.#children_field_name = children;
                }

                fn get_children(&self) -> &Children {
                    &self.#children_field_name
                }
            }
        }
    } else {
        quote! {}
    };

    let main_impl = quote! {
        impl ComponentProps for #struct_name {
            fn validate(&self) -> Result<(), String> {
                Ok(())
            }

            fn field_metadata() -> Vec<PropFieldMetadata> {
                vec![
                    #(#field_metadata_items),*
                ]
            }
        }

        impl PropRequirements for #struct_name {
            fn required_fields() -> &'static [&'static str] {
                &[#(#required_fields),*]
            }

            fn all_fields() -> &'static [(&'static str, &'static str)] {
                &[#(#all_fields),*]
            }
        }

        // Automatically implement Default with intelligent field handling
        impl Default for #struct_name {
            fn default() -> Self {
                Self {
                    #(#default_field_values),*
                }
            }
        }

        impl #struct_name {
            #(#field_accessors)*

            #(#builder_methods)*

            /// Create an instance with only the provided fields, using defaults for others
            /// This helps avoid clippy warnings about needless struct updates
            pub fn with_fields() -> Self {
                Self::default()
            }
        }

        // Include HasChildren implementation if applicable
        #has_children_impl
    };

    // Generate TrySetChildren implementation
    let try_set_children_impl = if has_children {
        quote! {
            impl TrySetChildren for #struct_name {
                fn try_set_children(&mut self, children: Children) -> bool {
                    self.set_children(children);
                    true
                }
            }
        }
    } else {
        quote! {
            impl TrySetChildren for #struct_name {
                fn try_set_children(&mut self, _children: Children) -> bool {
                    false
                }
            }
        }
    };

    quote! {
        #main_impl
        #try_set_children_impl
    }
}

/// Generate the ValidatedProps trait implementation with compile-time validation
fn generate_validated_props_impl(input: &DeriveInput) -> proc_macro2::TokenStream {
    let struct_name = &input.ident;

    // Extract fields from the struct
    let fields = match &input.data {
        Data::Struct(data_struct) => match &data_struct.fields {
            Fields::Named(fields_named) => &fields_named.named,
            _ => {
                return quote! {
                    compile_error!("ValidatedProps can only be derived for structs with named fields");
                };
            }
        },
        _ => {
            return quote! {
                compile_error!("ValidatedProps can only be derived for structs");
            };
        }
    };

    // Generate field validation metadata
    let field_metadata_items = fields.iter().enumerate().map(|(index, field)| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;
        let field_name_str = field_name.to_string();
        let field_type_str = quote! { #field_type }.to_string();
        let _field_id = index as u32;

        // Check if the field type is Option<T>
        let is_optional = is_option_type(field_type);

        // Parse validation attributes
        let validation_rules = parse_validation_attributes(&field.attrs);

        quote! {
            ValidatedFieldMetadata {
                name: #field_name_str.to_string(),
                type_name: #field_type_str.to_string(),
                is_optional: #is_optional,
                default_value: None,
                validation_rules: vec![#(#validation_rules),*],
            }
        }
    });

    // Generate compile-time field existence checks
    let field_existence_checks = fields.iter().enumerate().map(|(index, field)| {
        let _field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;
        let field_id = index as u32;

        // Generate HasField implementation
        let has_field_impl = quote! {
            impl HasField<FieldName<#field_id>> for #struct_name {}
        };

        // Generate HasFieldOfType implementation
        let has_field_of_type_impl = quote! {
            impl HasFieldOfType<FieldName<#field_id>, #field_type> for #struct_name {}
        };

        quote! {
            #has_field_impl
            #has_field_of_type_impl
        }
    });

    // Generate runtime validation logic
    let runtime_validations = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;

        // Parse validation attributes for runtime checks
        let validation_checks =
            generate_runtime_validation_checks(field_name, field_type, &field.attrs);

        quote! {
            #validation_checks
        }
    });

    // Generate field defaults for ValidatedProps
    let validated_field_defaults = fields.iter().map(|field| {
        let field_name = field.ident.as_ref().unwrap();
        let field_type = &field.ty;

        if is_option_type(field_type) {
            quote! { #field_name: None }
        } else {
            // For non-Option types, use Default::default()
            quote! { #field_name: Default::default() }
        }
    });

    quote! {
        // Implement ValidatedProps trait
        impl ValidatedProps for #struct_name {
            fn validation_metadata() -> Vec<ValidatedFieldMetadata> {
                vec![
                    #(#field_metadata_items),*
                ]
            }

            fn validate(&self) -> Result<(), Vec<ValidationError>> {
                let mut errors = Vec::new();
                #(#runtime_validations)*

                if errors.is_empty() {
                    Ok(())
                } else {
                    Err(errors)
                }
            }

            fn component_name() -> &'static str {
                stringify!(#struct_name)
            }
        }

        // Also implement ComponentProps for backward compatibility
        impl ComponentProps for #struct_name {
            fn validate(&self) -> Result<(), String> {
                match <Self as ValidatedProps>::validate(self) {
                    Ok(()) => Ok(()),
                    Err(errors) => {
                        let error_messages: Vec<String> = errors.iter().map(|e| e.to_string()).collect();
                        Err(error_messages.join("; "))
                    }
                }
            }

            fn field_metadata() -> Vec<PropFieldMetadata> {
                Self::validation_metadata().into_iter().map(|vm| {
                    PropFieldMetadata {
                        name: vm.name,
                        type_name: vm.type_name,
                        is_optional: vm.is_optional,
                        default_value: vm.default_value,
                    }
                }).collect()
            }
        }

        // Generate PropRequirements implementation for ValidatedProps
        impl PropRequirements for #struct_name {
            fn required_fields() -> &'static [&'static str] {
                // Extract required fields from validation metadata
                const REQUIRED_FIELDS: &[&str] = &[]; // TODO: Extract from metadata
                REQUIRED_FIELDS
            }

            fn all_fields() -> &'static [(&'static str, &'static str)] {
                // Extract all fields from validation metadata
                const ALL_FIELDS: &[(&str, &str)] = &[]; // TODO: Extract from metadata
                ALL_FIELDS
            }
        }

        // Implement Default for ValidatedProps
        impl Default for #struct_name {
            fn default() -> Self {
                Self {
                    #(#validated_field_defaults),*
                }
            }
        }

        // Generate compile-time field existence checks
        #(#field_existence_checks)*
    }
}

/// Parse validation attributes from field attributes
fn parse_validation_attributes(_attrs: &[syn::Attribute]) -> Vec<proc_macro2::TokenStream> {
    // For now, return empty validation rules
    // TODO: Implement full attribute parsing
    vec![]
}

/// Generate runtime validation checks for a field
fn generate_runtime_validation_checks(
    _field_name: &Ident,
    _field_type: &syn::Type,
    _attrs: &[syn::Attribute],
) -> proc_macro2::TokenStream {
    // For now, return empty validation
    // TODO: Implement runtime validation generation
    quote! {}
}

/// Helper function to check if a type is Option<T>
fn is_option_type(ty: &syn::Type) -> bool {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            return segment.ident == "Option";
        }
    }
    false
}

/// Helper function to check if a field has the #[children] attribute
fn has_children_attribute(attrs: &[syn::Attribute]) -> bool {
    attrs.iter().any(|attr| attr.path().is_ident("children"))
}

/// Helper function to check if a type is Children or Option<Children>
#[allow(dead_code)]
fn is_children_type(ty: &syn::Type) -> bool {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            if segment.ident == "Children" {
                return true;
            }
            // Check for Option<Children>
            if segment.ident == "Option" {
                if let syn::PathArguments::AngleBracketed(args) = &segment.arguments {
                    if let Some(syn::GenericArgument::Type(inner_type)) = args.args.first() {
                        return is_children_type(inner_type);
                    }
                }
            }
        }
    }
    false
}

/// Helper function to check if a type is Callback<T> or Callback<T, U>
fn _is_callback_type(ty: &syn::Type) -> bool {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            return segment.ident == "Callback";
        }
    }
    false
}

/// Helper function to extract callback input and output types
fn _extract_callback_types(ty: &syn::Type) -> Option<(syn::Type, Option<syn::Type>)> {
    if let syn::Type::Path(type_path) = ty {
        if let Some(segment) = type_path.path.segments.last() {
            if segment.ident == "Callback" {
                if let syn::PathArguments::AngleBracketed(args) = &segment.arguments {
                    let args: Vec<_> = args.args.iter().collect();
                    match args.len() {
                        1 => {
                            if let syn::GenericArgument::Type(input_type) = args[0] {
                                return Some((input_type.clone(), None));
                            }
                        }
                        2 => {
                            if let (
                                syn::GenericArgument::Type(input_type),
                                syn::GenericArgument::Type(output_type),
                            ) = (args[0], args[1])
                            {
                                return Some((input_type.clone(), Some(output_type.clone())));
                            }
                        }
                        _ => {}
                    }
                }
            }
        }
    }
    None
}
