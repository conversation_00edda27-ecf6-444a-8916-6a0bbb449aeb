use crate::errors::{Rsx<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RsxResult};
use crate::parsing::{RsxElement, RsxProp, RsxPropValue};

/// RSX validation engine with comprehensive error checking
pub struct RsxValidator;

impl RsxValidator {
    /// Validate an entire RSX element tree with comprehensive checks
    pub fn validate_element(element: &RsxElement) -> RsxResult<()> {
        Self::validate_element_with_context(
            element,
            &mut ValidationContext::new("root".to_string()),
        )
    }

    /// Validate an RSX element tree with context for better error reporting
    pub fn validate_element_with_context(
        element: &RsxElement,
        context: &mut ValidationContext,
    ) -> RsxResult<()> {
        match element {
            RsxElement::Element {
                name,
                props,
                children,
                ..
            } => {
                let element_name = name.segments.last().unwrap().ident.to_string();
                context.push_element(element_name.clone());

                // Validate element name with context
                Self::validate_element_name(name)?;

                // Validate props with context
                Self::validate_props(props)?;

                // Validate children recursively with context
                for child in children {
                    Self::validate_element_with_context(child, context)?;
                }

                // Validate element-specific rules with context
                Self::validate_element_rules(name, props, children)?;

                context.pop_element();
                Ok(())
            }
            RsxElement::Text { content, span } => Self::validate_text_content(content, *span),
            RsxElement::Expression { expr, span } => Self::validate_expression(expr, *span),
        }
    }

    /// Validate element name follows RSX conventions
    fn validate_element_name(name: &syn::Path) -> RsxResult<()> {
        if name.segments.is_empty() {
            return Err(RsxMacroError::element_name_error(
                proc_macro2::Span::call_site(),
                "Empty element name",
            ));
        }

        let element_name = name.segments.last().unwrap().ident.to_string();

        // Check if name starts with uppercase (PascalCase convention)
        if !element_name.chars().next().unwrap_or('a').is_uppercase() {
            return Err(RsxMacroError::element_name_error(
                proc_macro2::Span::call_site(),
                format!(
                    "Element name '{}' should start with uppercase letter (PascalCase)",
                    element_name
                ),
            ));
        }

        // Check for reserved names that might cause conflicts
        if Self::is_reserved_element_name(&element_name) {
            return Err(RsxMacroError::element_name_error(
                proc_macro2::Span::call_site(),
                format!("Element name '{}' is reserved", element_name),
            ));
        }

        Ok(())
    }

    /// Validate props for syntax and semantic correctness
    fn validate_props(props: &[RsxProp]) -> RsxResult<()> {
        for prop in props {
            Self::validate_prop(prop)?;
        }

        // Check for duplicate props
        Self::validate_no_duplicate_props(props)?;

        Ok(())
    }

    /// Validate a single prop
    fn validate_prop(prop: &RsxProp) -> RsxResult<()> {
        // Validate prop name
        Self::validate_prop_name(&prop.name, prop.span())?;

        // Validate prop value
        Self::validate_prop_value(&prop.value)?;

        Ok(())
    }

    /// Validate prop name follows conventions
    fn validate_prop_name(name: &proc_macro2::Ident, span: proc_macro2::Span) -> RsxResult<()> {
        // Check for valid identifier
        if name.to_string().is_empty() {
            return Err(RsxMacroError::attribute_syntax_error(
                span,
                name,
                "Empty attribute name",
            ));
        }

        // Check for reserved prop names
        if Self::is_reserved_prop_name(name) {
            return Err(RsxMacroError::attribute_syntax_error(
                span,
                name,
                format!("Attribute name '{}' is reserved", name),
            ));
        }

        Ok(())
    }

    /// Validate prop value
    fn validate_prop_value(value: &RsxPropValue) -> RsxResult<()> {
        match value {
            RsxPropValue::Literal { value, span } => Self::validate_literal_value(value, *span),
            RsxPropValue::Expression { expr, span } => Self::validate_expression(expr, *span),
        }
    }

    /// Validate literal values
    fn validate_literal_value(value: &str, span: proc_macro2::Span) -> RsxResult<()> {
        // For now, all string literals are valid
        // Future: Add validation for specific value formats (URLs, colors, etc.)
        if value.len() > 10000 {
            return Err(RsxMacroError::attribute_syntax_error(
                span,
                &proc_macro2::Ident::new("literal", span),
                "Literal value is too long (max 10000 characters)",
            ));
        }

        Ok(())
    }

    /// Validate expressions
    fn validate_expression(_expr: &syn::Expr, _span: proc_macro2::Span) -> RsxResult<()> {
        // For now, all expressions are valid
        // Future: Add validation for expression types, safety, etc.
        Ok(())
    }

    /// Validate text content
    fn validate_text_content(_content: &str, _span: proc_macro2::Span) -> RsxResult<()> {
        // For now, all text content is valid
        // Future: Add validation for text length, encoding, etc.
        Ok(())
    }

    /// Validate element-specific rules
    fn validate_element_rules(
        name: &syn::Path,
        _props: &[RsxProp],
        _children: &[RsxElement],
    ) -> RsxResult<()> {
        let element_name = name.segments.last().unwrap().ident.to_string();

        // Add element-specific validation rules
        match element_name.as_str() {
            "Text" => {
                // Text elements should have content prop or text children
                // This will be implemented in future iterations
            }
            _ => {
                // Generic element validation
            }
        }

        Ok(())
    }

    /// Check for duplicate props
    fn validate_no_duplicate_props(props: &[RsxProp]) -> RsxResult<()> {
        let mut seen_props = std::collections::HashSet::new();

        for prop in props {
            if seen_props.contains(&prop.name) {
                return Err(RsxMacroError::attribute_syntax_error(
                    prop.span(),
                    &prop.name,
                    format!("Duplicate attribute '{}'", prop.name),
                ));
            }
            seen_props.insert(&prop.name);
        }

        Ok(())
    }

    /// Check if element name is reserved
    fn is_reserved_element_name(name: &str) -> bool {
        const RESERVED_NAMES: &[&str] = &[
            "Element",
            "VirtualNode",
            "Component",
            "Fragment",
            "Slot",
            "Template",
        ];

        RESERVED_NAMES.contains(&name)
    }

    /// Check if prop name is reserved
    fn is_reserved_prop_name(name: &proc_macro2::Ident) -> bool {
        const RESERVED_PROPS: &[&str] = &["key", "ref", "children", "dangerouslySetInnerHTML"];

        RESERVED_PROPS.contains(&name.to_string().as_str())
    }

    /// Get validation suggestions for common RSX issues
    pub fn _get_validation_suggestions(error: &RsxMacroError) -> Vec<String> {
        match error {
            RsxMacroError::Syntax { .. } => vec![
                "Check RSX syntax follows JSX-like format".to_string(),
                "Ensure proper opening and closing tags".to_string(),
                "Example: rsx! { <Text content=\"Hello\" /> }".to_string(),
            ],
            RsxMacroError::ElementName { .. } => vec![
                "Use PascalCase for component names".to_string(),
                "Ensure the component is imported and available".to_string(),
                "Example: <Text>, <MyCustomComponent>".to_string(),
            ],
            RsxMacroError::AttributeSyntax { .. } => vec![
                "Use proper attribute syntax: name=\"value\" or name={expression}".to_string(),
                "String literals should be quoted".to_string(),
                "Expressions should be wrapped in braces".to_string(),
                "Example: <Text content=\"Hello\" style={my_style} />".to_string(),
            ],
            // RsxMacroError::Expression { .. } => vec![
            //     "Ensure expressions evaluate to valid types".to_string(),
            //     "Use proper Rust syntax within braces".to_string(),
            //     "Example: {variable}, {function_call()}, {if condition { value } else { other }}"
            //         .to_string(),
            // ],
            // RsxMacroError::Nesting { .. } => vec![
            //     "Check element nesting rules".to_string(),
            //     "Some elements cannot contain certain children".to_string(),
            //     "Refer to component documentation for valid children".to_string(),
            // ],
            // RsxMacroError::MissingAttribute { .. } => vec![
            //     "Add the required attribute to the element".to_string(),
            //     "Check component documentation for required props".to_string(),
            //     "Use proper attribute syntax with quotes or braces".to_string(),
            // ],
        }
    }

    /// Validate RSX syntax at compile time
    pub fn _validate_compile_time(element: &RsxElement) -> proc_macro2::TokenStream {
        match Self::validate_element(element) {
            Ok(()) => {
                // Generate validation success marker
                quote::quote! {
                    // RSX validation passed at compile time
                    const _: () = ();
                }
            }
            Err(error) => {
                // Generate compile error
                let error_msg = format!("RSX validation failed: {:?}", error);
                quote::quote! {
                    compile_error!(#error_msg);
                }
            }
        }
    }
}

/// Helper trait for RSX validation
pub trait RsxValidate {
    /// Validate this RSX element
    fn validate(&self) -> RsxResult<()>;
}

impl RsxValidate for RsxElement {
    fn validate(&self) -> RsxResult<()> {
        RsxValidator::validate_element(self)
    }
}

/// Validation context for enhanced error reporting
#[derive(Debug, Clone)]
pub struct ValidationContext {
    pub element_path: Vec<String>,
    pub current_element: String,
    pub parent_element: Option<String>,
}

impl ValidationContext {
    /// Create a new validation context
    pub fn new(element_name: String) -> Self {
        Self {
            element_path: vec![element_name.clone()],
            current_element: element_name,
            parent_element: None,
        }
    }

    /// Push a new element onto the context
    pub fn push_element(&mut self, element_name: String) {
        self.parent_element = Some(self.current_element.clone());
        self.current_element = element_name.clone();
        self.element_path.push(element_name);
    }

    /// Pop an element from the context
    pub fn pop_element(&mut self) {
        if self.element_path.len() > 1 {
            self.element_path.pop();
            self.current_element = self.element_path.last().unwrap().clone();
            self.parent_element = if self.element_path.len() > 1 {
                Some(self.element_path[self.element_path.len() - 2].clone())
            } else {
                None
            };
        }
    }

    /// Get the current element path as a string
    pub fn _path_string(&self) -> String {
        self.element_path.join(" > ")
    }
}
