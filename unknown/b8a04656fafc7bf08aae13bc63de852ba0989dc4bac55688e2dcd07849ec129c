use crossterm::event::{Event, KeyC<PERSON>, Key<PERSON>od<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>vent<PERSON><PERSON>};
use std::time::{SystemTime, UNIX_EPOCH};
use terminus_ui::prelude::*;

/// Example demonstrating React-style use_event hook usage
/// This showcases comprehensive event handling capabilities for terminal UI interactions

// Keyboard event handler component
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
struct KeyboardHandlerProps {
    pub title: String,
}

#[component(KeyboardHandler)]
fn keyboard_handler(props: KeyboardHandlerProps) -> Element {
    let (key_count, set_key_count) = use_state(0);
    let (last_key, set_last_key) = use_state("None".to_string());
    let (quit_requested, set_quit_requested) = use_state(false);

    // Listen for keyboard events
    let key_event = use_event();

    // Handle keyboard events directly
    if let Some(crossterm::event::Event::Key(key_event)) = key_event {
        if key_event.is_press() {
            // Handle key events
            let key_info = match key_event.code {
                KeyCode::Char(c) => {
                    if key_event.modifiers.contains(KeyModifiers::CONTROL) {
                        format!("Ctrl+{}", c)
                    } else if key_event.modifiers.contains(KeyModifiers::ALT) {
                        format!("Alt+{}", c)
                    } else {
                        format!("'{}'", c)
                    }
                }
                KeyCode::Enter => "Enter".to_string(),
                KeyCode::Esc => "Escape".to_string(),
                KeyCode::Tab => "Tab".to_string(),
                KeyCode::Backspace => "Backspace".to_string(),
                KeyCode::Delete => "Delete".to_string(),
                KeyCode::Up => "Up Arrow".to_string(),
                KeyCode::Down => "Down Arrow".to_string(),
                KeyCode::Left => "Left Arrow".to_string(),
                KeyCode::Right => "Right Arrow".to_string(),
                KeyCode::F(n) => format!("F{}", n),
                _ => format!("{:?}", key_event.code),
            };

            set_last_key.set(key_info);
            set_key_count.update(|prev| prev + 1);

            // Check for quit command
            if key_event.code == KeyCode::Char('q')
                && key_event.modifiers.contains(KeyModifiers::CONTROL)
            {
                set_quit_requested.set(true);
            }
        }
    }

    let key_count_value = key_count.get();
    let last_key_value = last_key.get();
    let quit_requested_value = quit_requested.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(if quit_requested_value { Color::Red } else { Color::Green })}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("⌨️  Last Key: {}", last_key_value)} />
                <Text content={format!("🔢 Key Count: {}", key_count_value)} />
                <Text content={format!("🚪 Quit Requested: {}", quit_requested_value)} />
                <Text content="Press any key to see events" />
                <Text content="Ctrl+Q to request quit" />
            </Layout>
        </Block>
    }
}

// Mouse event handler component
#[derive(Props, Debug, Clone)]
struct MouseHandlerProps {
    pub title: String,
}

#[component(MouseHandler)]
fn mouse_handler(props: MouseHandlerProps) -> Element {
    let (mouse_pos, set_mouse_pos) = use_state((0u16, 0u16));
    let (click_count, set_click_count) = use_state(0);
    let (last_action, set_last_action) = use_state("None".to_string());

    // Listen for mouse events
    let mouse_event = use_event();

    // Handle mouse events directly
    if let Some(Event::Mouse(mouse_event)) = mouse_event {
        let mouse = mouse_event;
        set_mouse_pos.set((mouse.column, mouse.row));
        let action = match mouse.kind {
            MouseEventKind::Down(MouseButton::Left) => {
                set_click_count.update(|prev| prev + 1);
                "Left Click"
            }
            MouseEventKind::Down(MouseButton::Right) => {
                set_click_count.update(|prev| prev + 1);
                "Right Click"
            }
            MouseEventKind::Down(MouseButton::Middle) => {
                set_click_count.update(|prev| prev + 1);
                "Middle Click"
            }
            MouseEventKind::Up(_) => "Button Release",
            MouseEventKind::Drag(MouseButton::Left) => "Left Drag",
            MouseEventKind::Drag(MouseButton::Right) => "Right Drag",
            MouseEventKind::Moved => "Mouse Move",
            MouseEventKind::ScrollDown => "Scroll Down",
            MouseEventKind::ScrollUp => "Scroll Up",
            _ => "Other",
        };

        set_last_action.set(action.to_string());
    }

    let (mouse_x, mouse_y) = mouse_pos.get();
    let click_count_value = click_count.get();
    let last_action_value = last_action.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("🖱️  Position: ({}, {})", mouse_x, mouse_y)} />
                <Text content={format!("👆 Click Count: {}", click_count_value)} />
                <Text content={format!("🎯 Last Action: {}", last_action_value)} />
                <Text content="Move mouse and click to see events" />
                <Text content="Try scrolling and dragging" />
            </Layout>
        </Block>
    }
}

// Terminal resize handler component
#[derive(Props, Debug, Clone)]
struct ResizeHandlerProps {
    pub title: String,
}

#[component(ResizeHandler)]
fn resize_handler(props: ResizeHandlerProps) -> Element {
    let (terminal_size, set_terminal_size) = use_state((0u16, 0u16));
    let (resize_count, set_resize_count) = use_state(0);
    let (last_resize_time, set_last_resize_time) = use_state("Never".to_string());

    // Listen for resize events
    if let Some(Event::Resize(width, height)) = use_event() {
        set_terminal_size.set((width, height));
        set_resize_count.update(|prev| prev + 1);
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        set_last_resize_time.set(format!("Timestamp: {}", timestamp));
    }

    let (width, height) = terminal_size.get();
    let resize_count_value = resize_count.get();
    let last_resize_time_value = last_resize_time.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("📐 Terminal Size: {}x{}", width, height)} />
                <Text content={format!("🔄 Resize Count: {}", resize_count_value)} />
                <Text content={format!("⏰ Last Resize: {}", last_resize_time_value)} />
                <Text content="Resize the terminal to see events" />
            </Layout>
        </Block>
    }
}

// Generic event listener component
#[derive(Props, Debug, Clone)]
struct GenericEventListenerProps {
    pub title: String,
}

#[component(GenericEventListener)]
fn generic_event_listener(props: GenericEventListenerProps) -> Element {
    let (total_events, set_total_events) = use_state(0);
    let (last_event_type, set_last_event_type) = use_state("None".to_string());
    let (event_history, set_event_history) = use_state(Vec::<String>::new());

    // Listen for any terminal event
    let any_event = use_event();

    // Handle any event directly
    if let Some(event) = any_event {
        set_total_events.update(|prev| prev + 1);

        let event_type = match event {
            Event::Key(_) => "Keyboard",
            Event::Mouse(_) => "Mouse",
            Event::Resize(_, _) => "Resize",
            Event::FocusGained => "Focus Gained",
            Event::FocusLost => "Focus Lost",
            Event::Paste(_) => "Paste",
        };

        set_last_event_type.set(event_type.to_string());

        // Add to history (keep last 5)
        set_event_history.update(|mut prev| {
            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_millis();
            prev.push(format!("{}: {}", event_type, timestamp % 100000));
            if prev.len() > 5 {
                prev.remove(0);
            }
            prev
        });
    }

    let total_events_value = total_events.get();
    let last_event_type_value = last_event_type.get();
    let event_history_value = event_history.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("📊 Total Events: {}", total_events_value)} />
                <Text content={format!("🎯 Last Event: {}", last_event_type_value)} />
                <Text content="📝 Recent Events:" />
                {event_history_value.iter().map(|entry| {
                    rsx! { <Text content={format!("  • {}", entry)} /> }
                }).collect::<Vec<_>>()}
                <Text content="This component listens to ALL events" />
            </Layout>
        </Block>
    }
}

// Main application component
#[derive(Props, Debug, Clone)]
struct EventDemoAppProps {
    pub title: String,
}

#[component(EventDemoApp)]
fn event_demo_app(props: EventDemoAppProps) -> Element {
    let (_app_running, _set_app_running) = use_state(true);

    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="🎮 React-style Event Handling Demo for Terminus UI" />
                    <Text content="This demo showcases comprehensive terminal event handling" />
                    <Text content="Try keyboard input, mouse interaction, and terminal resizing" />
                </Layout>
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),
                    Constraint::Percentage(50),
                ]}
            >
                <KeyboardHandler title={"⌨️  Keyboard Events".to_string()} />
                <MouseHandler title={"🖱️  Mouse Events".to_string()} />
            </Layout>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),
                    Constraint::Percentage(50),
                ]}
            >
                <ResizeHandler title={"📐 Resize Events".to_string()} />
                <GenericEventListener title={"📊 All Events".to_string()} />
            </Layout>

            <Block
                title="📋 Event System Features Demonstrated"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="✅ Keyboard Events - Key presses, modifiers, special keys" />
                    <Text content="✅ Mouse Events - Clicks, drags, scrolls, movement" />
                    <Text content="✅ Terminal Resize Events - Window size changes" />
                    <Text content="✅ Generic Event Handling - All event types" />
                    <Text content="✅ React-style Hooks - Automatic re-renders on events" />
                    <Text content="✅ Event Integration - Works with useState and useEffect" />
                    <Text content="✅ Type Safety - Compile-time event type checking" />
                    <Text content="✅ Thread Safety - Safe concurrent event handling" />
                </Layout>
            </Block>
        </Layout>
    }
}

// Main function
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚀 Starting Event Handling Demo...");

    let app_props = EventDemoAppProps {
        title: "🎮 use_event Hook Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <EventDemoApp title={app_props.title} />
    };

    println!("🎯 Running comprehensive event handling demo...");
    println!("📝 This demo showcases React-style event handling:");
    println!("   - Keyboard event handling with modifiers");
    println!("   - Mouse event handling (clicks, drags, scrolls)");
    println!("   - Terminal resize event handling");
    println!("   - Generic event listening for all event types");
    println!("   - Integration with useState and useEffect hooks");
    println!("   - Type-safe event conversion and filtering");

    // Run the UI
    render_async(element).await?;

    println!("✅ Event handling demo completed!");
    Ok(())
}
