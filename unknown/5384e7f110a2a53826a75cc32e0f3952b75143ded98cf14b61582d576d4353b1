use std::sync::{<PERSON>, Mutex};
use std::time::Duration;
use terminus_ui::prelude::*;

// Shared counter state for dynamic updates
type CounterState = Arc<Mutex<i32>>;

// Example props for dynamic async components
#[derive(Props, Debug, Clone)]
struct DynamicCounterProps {
    pub counter_state: CounterState,
    pub title: String,
    pub color: Color,
}

// Dynamic counter component that reads from shared state
#[component(DynamicCounter)]
fn dynamic_counter(props: DynamicCounterProps) -> Element {
    // Read the current counter value from shared state
    let current_value = {
        let counter = props.counter_state.lock().unwrap();
        *counter
    };

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(props.color)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("🔢 Counter: {}", current_value)} />
                <Text content={format!("📊 Value: {}", current_value * 10)} />
                <Text content={format!("⚡ Status: {}", if current_value % 2 == 0 { "Even" } else { "Odd" })} />
            </Layout>
        </Block>
    }
}

// Main async application with dynamic counters
#[derive(Props, Debug, Clone)]
struct DynamicAppProps {
    pub title: String,
    pub counter1: CounterState,
    pub counter2: CounterState,
    pub counter3: CounterState,
}

#[component(DynamicApp)]
fn dynamic_app(props: DynamicAppProps) -> Element {
    let counter1 = props.counter1.clone();
    let counter2 = props.counter2.clone();
    let counter3 = props.counter3.clone();

    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
            >
                <Text content="🚀 Welcome to Dynamic Async Terminus UI! 🚀" />
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(33),
                    Constraint::Percentage(33),
                    Constraint::Percentage(34),
                ]}
            >
                <DynamicCounter
                    counter_state={counter1}
                    title={"⚡ Fast Counter".to_string()}
                    color={Color::Cyan}
                />

                <DynamicCounter
                    counter_state={counter2}
                    title={"🐌 Slow Counter".to_string()}
                    color={Color::Magenta}
                />

                <DynamicCounter
                    counter_state={counter3}
                    title={"🎲 Random Counter".to_string()}
                    color={Color::Yellow}
                />
            </Layout>

            <Block
                title="📋 Instructions & Status"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Text content="🔄 This demo shows LIVE dynamic async rendering!\n⚡ Fast counter updates every 500ms\n🐌 Slow counter updates every 2 seconds\n🎲 Random counter updates at random intervals\n\n📝 Press 'q' or 'Esc' to quit\n🎯 All counters update concurrently while UI remains responsive!" />
            </Block>
        </Layout>
    }
}

// Main function that sets up and runs the Tokio-based async demo
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    run_async_demo().await
}

// Async main function demonstrating dynamic concurrent operations
async fn run_async_demo() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚀 Starting dynamic async terminus-ui demo...");

    // Create shared counter states
    let fast_counter = Arc::new(Mutex::new(0i32));
    let slow_counter = Arc::new(Mutex::new(100i32));
    let random_counter = Arc::new(Mutex::new(50i32));

    // Clone counters for background tasks
    let fast_counter_bg = fast_counter.clone();
    let slow_counter_bg = slow_counter.clone();
    let random_counter_bg = random_counter.clone();

    // We'll start background tasks using threads instead of async tasks
    println!("⚡ Starting background counter tasks...");

    // Create the dynamic UI element
    let app_props = DynamicAppProps {
        title: "🔄 Dynamic Async Terminus UI Demo".to_string(),
        counter1: fast_counter,
        counter2: slow_counter,
        counter3: random_counter,
    };

    let element = rsx! {
        <DynamicApp
            title={app_props.title}
            counter1={app_props.counter1}
            counter2={app_props.counter2}
            counter3={app_props.counter3}
        />
    };

    println!("🎯 Running dynamic UI with live counter updates...");
    println!("📊 Watch the counters update in real-time!");
    println!("🔄 The UI will automatically refresh to show counter changes!");

    // Start background tasks using Tokio
    start_tokio_counter_tasks(fast_counter_bg, slow_counter_bg, random_counter_bg).await;

    // Run the UI - it will automatically re-render and show the updated counter values
    render_async(element).await
}

// Start background tasks that update counters using Tokio
async fn start_tokio_counter_tasks(
    fast_counter: CounterState,
    slow_counter: CounterState,
    random_counter: CounterState,
) {
    // Fast counter task - updates every 500ms
    let fast_counter_clone = fast_counter.clone();
    tokio::spawn(async move {
        loop {
            {
                let mut c = fast_counter_clone.lock().unwrap();
                *c += 1;
                if *c > 999 {
                    *c = 0;
                } // Reset to prevent overflow
            }
            tokio::time::sleep(Duration::from_millis(500)).await;
        }
    });

    // Slow counter task - updates every 2 seconds
    let slow_counter_clone = slow_counter.clone();
    tokio::spawn(async move {
        loop {
            {
                let mut c = slow_counter_clone.lock().unwrap();
                *c += 5;
                if *c > 999 {
                    *c = 100;
                } // Reset to prevent overflow
            }
            tokio::time::sleep(Duration::from_secs(2)).await;
        }
    });

    // Random counter task - updates at random intervals
    let random_counter_clone = random_counter.clone();
    tokio::spawn(async move {
        let mut seed = 42u32;
        loop {
            {
                let mut c = random_counter_clone.lock().unwrap();
                // Simple pseudo-random number generator
                seed = seed.wrapping_mul(1103515245).wrapping_add(12345);
                let random_change = (seed % 21) as i32 - 10; // -10 to +10
                *c += random_change;
                *c = (*c).clamp(0, 999);
            }
            // Random delay between 800ms and 3000ms
            let delay_ms = 800 + (seed % 2200) as u64;
            tokio::time::sleep(Duration::from_millis(delay_ms)).await;
        }
    });

    println!("✅ Tokio background counter tasks started!");
}
