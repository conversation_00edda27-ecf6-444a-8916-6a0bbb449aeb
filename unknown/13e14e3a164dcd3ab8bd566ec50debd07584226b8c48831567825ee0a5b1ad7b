use terminus_ui::prelude::*;

/// Example demonstrating React-style useState hook usage
/// This showcases all the key features of the useState implementation

// Simple counter component demonstrating basic useState
#[derive(Props, Debug, Clone)]
struct CounterProps {
    pub title: String,
    pub initial_count: i32,
}

#[component(Counter)]
fn counter(props: CounterProps) -> Element {
    // Basic useState with initial value
    let (count_handle, _set_count) = use_state(props.initial_count);
    let count = count_handle.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("🔢 Count: {}", count)} />
                <Text content={format!("📊 Double: {}", count * 2)} />
                <Text content={format!("⚡ Status: {}", if count % 2 == 0 { "Even" } else { "Odd" })} />
                <Text content="Press 'i' to increment, 'd' to decrement, 'r' to reset" />
            </Layout>
        </Block>
    }
}

// Complex state example with nested data
#[derive(<PERSON>lone, Debug)]
struct AppState {
    user_name: String,
    score: u32,
    level: u8,
    achievements: Vec<String>,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            user_name: "Player".to_string(),
            score: 0,
            level: 1,
            achievements: vec!["🎯 First Steps".to_string()],
        }
    }
}

#[derive(Props, Debug, Clone)]
struct GameStateProps {
    pub title: String,
}

#[component(GameState)]
fn game_state(props: GameStateProps) -> Element {
    // Complex state management
    let (state_handle, _set_state) = use_state(AppState::default());

    // Use field access for efficient property access
    let user_name = state_handle.field(|s| s.user_name.clone());
    let score = state_handle.field(|s| s.score);
    let level = state_handle.field(|s| s.level);
    let achievements_count = state_handle.field(|s| s.achievements.len());
    let recent_achievements =
        state_handle.field(|s| s.achievements.iter().take(3).cloned().collect::<Vec<_>>());

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("👤 Player: {}", user_name)} />
                <Text content={format!("🏆 Score: {}", score)} />
                <Text content={format!("📈 Level: {}", level)} />
                <Text content={format!("🎖️  Achievements: {}", achievements_count)} />

                <Block
                    title="Recent Achievements"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    <Layout direction={Direction::Vertical}>
                        {recent_achievements.iter().map(|achievement| {
                            rsx! { <Text content={achievement.clone()} /> }
                        }).collect::<Vec<_>>()}
                    </Layout>
                </Block>

                <Text content="Press 's' to add score, 'l' to level up, 'a' to add achievement" />
            </Layout>
        </Block>
    }
}

// Multiple useState hooks in one component
#[derive(Props, Debug, Clone)]
struct MultiStateProps {
    pub title: String,
}

#[component(MultiState)]
fn multi_state(props: MultiStateProps) -> Element {
    // Multiple independent state hooks
    let (name_handle, _set_name) = use_state("Anonymous".to_string());
    let (age_handle, _set_age) = use_state(25u8);
    let (is_active_handle, _set_is_active) = use_state(true);
    let (tags_handle, _set_tags) = use_state(vec!["rust".to_string(), "ui".to_string()]);

    // Get values from handles
    let name = name_handle.get();
    let age = age_handle.get();
    let is_active = is_active_handle.get();
    let tags = tags_handle.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("📝 Name: {}", name)} />
                <Text content={format!("🎂 Age: {}", age)} />
                <Text content={format!("✅ Active: {}", if is_active { "Yes" } else { "No" })} />
                <Text content={format!("🏷️  Tags: {}", tags.join(", "))} />

                <Text content="Press 'n' to change name, '+'/'-' for age, 't' to toggle active" />
            </Layout>
        </Block>
    }
}

// Main application component
#[derive(Props, Debug, Clone)]
struct UseStateAppProps {
    pub title: String,
}

#[component(UseStateApp)]
fn use_state_app(props: UseStateAppProps) -> Element {
    // App-level state for demonstration
    let (current_tab_handle, _set_current_tab) = use_state(0usize);
    let (demo_counter_handle, _set_demo_counter) = use_state(0u32);

    let current_tab = current_tab_handle.get();
    let demo_counter = demo_counter_handle.get();

    let tabs = ["Counter", "Game State", "Multi State"];
    let current_tab_name = tabs.get(current_tab).unwrap_or(&"Unknown");

    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Blue)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="🚀 React-style useState Hook Demo for Terminus UI" />
                    <Text content={format!("📊 Demo Counter: {} (auto-increments)", demo_counter)} />
                    <Text content={format!("📑 Current Tab: {} ({})", current_tab + 1, current_tab_name)} />
                    <Text content="Press 'Tab' to switch tabs, 'q' to quit" />
                </Layout>
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(100),
                ]}
            >
                {match current_tab {
                    0 => rsx! {
                        <Counter
                            title={"🔢 useState Counter Demo".to_string()}
                            initial_count={0}
                        />
                    },
                    1 => rsx! {
                        <GameState
                            title={"🎮 Complex State Demo".to_string()}
                        />
                    },
                    2 => rsx! {
                        <MultiState
                            title={"🔄 Multiple useState Demo".to_string()}
                        />
                    },
                    _ => rsx! {
                        <Block>
                            <Text content="Unknown tab" />
                        </Block>
                    }
                }}
            </Layout>

            <Block
                title="📋 useState Features Demonstrated"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="✅ Basic state management with initial values" />
                    <Text content="✅ Direct value updates: setState(newValue)" />
                    <Text content="✅ Functional updates: setState(prev => newValue)" />
                    <Text content="✅ Complex state objects with nested data" />
                    <Text content="✅ Multiple useState hooks in single component" />
                    <Text content="✅ State persistence across re-renders" />
                    <Text content="✅ Thread-safe state updates" />
                    <Text content="✅ Memory-efficient Arc-based sharing" />
                </Layout>
            </Block>
        </Layout>
    }
}

// Async demo showing thread-safe useState
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚀 Starting useState Hook Demo...");

    // Create the main app
    let app_props = UseStateAppProps {
        title: "🔄 useState Hook Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <UseStateApp title={app_props.title} />
    };

    println!("🎯 Running useState demo with live state management...");
    println!("📝 This demo showcases React-style useState behavior:");
    println!("   - State persistence across renders");
    println!("   - Thread-safe state updates");
    println!("   - Functional and direct updates");
    println!("   - Complex state management");

    // Run the UI
    render_async(element).await?;

    println!("✅ useState demo completed!");
    Ok(())
}
