use syn::{
    Expr, Ident, LitStr, Path, Result, Token,
    parse::{Parse, ParseStream},
    token::{<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>},
};

/// Represents an RSX element with enhanced structure analysis
#[derive(Debug, <PERSON>lone)]
pub enum RsxElement {
    /// A component or widget element
    Element {
        name: Path,
        props: Vec<RsxProp>,
        children: Vec<RsxElement>,
    },
    /// A text node
    Text {
        content: String,
        span: proc_macro2::Span,
    },
    /// An expression that evaluates to an element
    Expression { expr: Expr, span: proc_macro2::Span },
}

/// Represents a prop in an RSX element with enhanced validation
#[derive(Debug, Clone)]
pub struct RsxProp {
    pub name: Ident,
    pub value: RsxPropValue,
    pub span: proc_macro2::Span,
}

/// Represents the value of a prop with type information
#[derive(Debug, <PERSON>lone)]
pub enum RsxPropValue {
    /// A literal string value
    Literal {
        value: String,
        span: proc_macro2::Span,
    },
    /// An expression that evaluates to a value
    Expression {
        expr: Box<Expr>,
        span: proc_macro2::Span,
    },
}

impl RsxProp {
    /// Get the span of this prop for error reporting
    pub fn span(&self) -> proc_macro2::Span {
        self.span
    }
}

impl Parse for RsxElement {
    fn parse(input: ParseStream) -> Result<Self> {
        // Use simple parsing approach to avoid error type conflicts for now
        if input.peek(LitStr) {
            // Text node
            let lit: LitStr = input.parse()?;
            Ok(RsxElement::Text {
                content: lit.value(),
                span: lit.span(),
            })
        } else if input.peek(Brace) {
            // Expression
            let content;
            syn::braced!(content in input);
            let expr: Expr = content.parse()?;
            Ok(RsxElement::Expression {
                expr,
                span: input.span(),
            })
        } else if input.peek(Lt) {
            // Element
            input.parse::<Lt>()?; // <
            let name: Path = input.parse()?;

            let mut props = Vec::new();

            // Parse props
            while !input.peek(Gt) && !input.peek(Token![/]) {
                let prop_span = input.span();
                let prop_name: Ident = input.parse()?;
                input.parse::<Token![=]>()?;

                let prop_value = if input.peek(LitStr) {
                    let lit: LitStr = input.parse()?;
                    RsxPropValue::Literal {
                        value: lit.value(),
                        span: lit.span(),
                    }
                } else if input.peek(Brace) {
                    let content;
                    syn::braced!(content in input);
                    let expr: Expr = content.parse()?;
                    RsxPropValue::Expression {
                        expr: Box::new(expr),
                        span: prop_span,
                    }
                } else {
                    return Err(input.error("Expected string literal or expression"));
                };

                props.push(RsxProp {
                    name: prop_name,
                    value: prop_value,
                    span: prop_span,
                });
            }

            let mut children = Vec::new();

            if input.peek(Token![/]) {
                // Self-closing tag
                input.parse::<Token![/]>()?;
                input.parse::<Gt>()?;
            } else {
                // Opening tag with children
                input.parse::<Gt>()?;

                // Parse children
                while !input.peek(Lt) || !input.peek2(Token![/]) {
                    if input.is_empty() {
                        break;
                    }
                    children.push(input.parse()?);
                }

                // Closing tag
                if !input.is_empty() {
                    input.parse::<Lt>()?;
                    input.parse::<Token![/]>()?;
                    let _closing_name: Path = input.parse()?;
                    input.parse::<Gt>()?;
                }
            }

            Ok(RsxElement::Element {
                name,
                props,
                children,
            })
        } else {
            Err(input.error("Expected element, text, or expression"))
        }
    }
}
