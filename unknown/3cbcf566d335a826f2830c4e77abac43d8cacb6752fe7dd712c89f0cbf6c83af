/// Example demonstrating the modal/dialog component pattern
/// This showcases the Shadcn UI-like Dialog API for terminal applications
use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Props for the main demo app
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct ModalDemoAppProps {
    pub title: String,
}

/// Main demo app component
#[component(ModalDemoApp)]
fn modal_demo_app(props: ModalDemoAppProps) -> Element {
    let (is_modal_open, set_is_modal_open) = use_state(false);
    let (modal_content, set_modal_content) = use_state("Welcome to the Modal Demo!".to_string());
    let (button_count, set_button_count) = use_state(0);

    // Handle keyboard events for demo interactions
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('o') | KeyCode::Char('O') => {
                // Open modal with welcome message
                set_modal_content.set("Welcome to the Modal Demo!\n\nThis modal demonstrates the Shadcn UI-like Dialog API.\n\nPress 'Escape' to close.".to_string());
                set_is_modal_open.set(true);
            }
            KeyCode::Char('i') | KeyCode::Char('I') => {
                // Open modal with info
                set_modal_content.set("Information Modal\n\nThis is an informational dialog.\nYou can put any content here.\n\nPress 'Escape' to close.".to_string());
                set_is_modal_open.set(true);
            }
            KeyCode::Char('c') | KeyCode::Char('C') => {
                // Open modal with counter
                let count = button_count.get();
                set_button_count.set(count + 1);
                set_modal_content.set(format!("Counter Modal\n\nButton pressed {} times!\n\nPress 'c' again to increment.\nPress 'Escape' to close.", count + 1));
                set_is_modal_open.set(true);
            }
            _ => {}
        }
    }

    // Callback for when modal should be closed
    let on_modal_change = {
        let set_is_modal_open = set_is_modal_open.clone();
        move |open: bool| {
            set_is_modal_open.set(open);
        }
    };

    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Modal/Dialog Component Demo - Shadcn UI-like API" />
            </Block>

            // Main content
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(3),
                    Constraint::Length(3),
                    Constraint::Length(3),
                    Constraint::Min(1),
                ]}
            >
                // Instructions
                <Block
                    title={"Instructions".to_string()}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green)}
                >
                    <Text content="Press 'o' for Welcome Modal | 'i' for Info Modal | 'c' for Counter Modal | 'q' to quit" />
                </Block>

                // Status
                <Block
                    title={"Status".to_string()}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    <Text content={format!("Modal Open: {} | Button Count: {} | Debug: Modal state = {}",
                        if is_modal_open.get() { "Yes" } else { "No" },
                        button_count.get(),
                        is_modal_open.get())} />
                </Block>

                // Demo area
                <Block
                    title={"Demo Area".to_string()}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Magenta)}
                >
                    <Text content="This area shows the modal overlay when opened. The modal will appear centered over this content." />
                </Block>

                // Footer
                <Block
                    title={"Modal API Example".to_string()}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Blue)}
                >
                    <Text content="The modal uses a Shadcn UI-like API: Dialog with open prop and onOpenChange callback" />
                </Block>
            </Layout>

            // Modal Dialog - This demonstrates the Shadcn UI-like API
            <Dialog open={is_modal_open.get()} on_open_change={on_modal_change}>
                <DialogTitle>
                    <Text content="Modal Dialog Title" />
                </DialogTitle>
                <DialogContent>
                    <Text content={modal_content.get()} />
                </DialogContent>
            </Dialog>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_props = ModalDemoAppProps {
        title: "🪟 Modal Component Demo".to_string(),
    };

    let element = rsx! {
        <ModalDemoApp title={app_props.title} />
    };

    render(element)
}
