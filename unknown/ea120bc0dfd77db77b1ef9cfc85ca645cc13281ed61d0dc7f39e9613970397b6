use terminus_ui::prelude::*;

// Example of a basic component (no frame/area access)
#[derive(<PERSON><PERSON>, Debug, Clone)]
pub struct BasicCardProps {
    pub title: String,
    pub content: String,
}

#[component(BasicCard)]
fn basic_card(props: BasicCardProps) -> Element {
    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Text content={props.content} />
        </Block>
    }
}

// Example of a component with area access
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct AreaAwareCardProps {
    pub title: String,
    pub content: String,
}

#[component(AreaAwareCard)]
fn area_aware_card(props: AreaAwareCardProps, area: Rect) -> Element {
    // Component can access the rendering area for advanced layout decisions
    let size_info = format!(
        "Area: {}x{} at ({}, {})",
        area.width, area.height, area.x, area.y
    );

    let enhanced_content = format!("{}\n\n[Area Info: {}]", props.content, size_info);

    rsx! {
        <Block
            title={format!("{} (Area-Aware)", props.title)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Text content={enhanced_content} />
        </Block>
    }
}

// Example of a component with frame access
#[derive(Props, Debug, Clone)]
pub struct FrameAwareCardProps {
    pub title: String,
    pub content: String,
}

#[component(FrameAwareCard)]
fn frame_aware_card(props: FrameAwareCardProps, frame: &mut Frame) -> Element {
    // Component can access the frame for terminal capabilities
    let terminal_size = frame.area();
    let frame_info = format!("Terminal: {}x{}", terminal_size.width, terminal_size.height);

    let enhanced_content = format!("{}\n\n[Frame Info: {}]", props.content, frame_info);

    rsx! {
        <Block
            title={format!("{} (Frame-Aware)", props.title)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Text content={enhanced_content} />
        </Block>
    }
}

// Example of a component with both frame and area access
#[derive(Props, Debug, Clone)]
pub struct FullAccessCardProps {
    pub title: String,
    pub content: String,
}

#[component(FullAccessCard)]
fn full_access_card(props: FullAccessCardProps, frame: &mut Frame, area: Rect) -> Element {
    // Component can access both frame and area for maximum flexibility
    let terminal_size = frame.area();
    let area_percentage = (
        area.width * 100 / terminal_size.width,
        area.height * 100 / terminal_size.height,
    );

    let enhanced_content = format!(
        "{}\n\n[Terminal: {}x{}]\n[Area: {}x{} ({}%, {}% of terminal)]",
        props.content,
        terminal_size.width,
        terminal_size.height,
        area.width,
        area.height,
        area_percentage.0,
        area_percentage.1
    );

    rsx! {
        <Block
            title={format!("{} (Full Access)", props.title)}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Text content={enhanced_content} />
        </Block>
    }
}

// Main dashboard component
#[derive(Props, Debug, Clone)]
pub struct DashboardProps {
    pub title: String,
}

#[component(Dashboard)]
fn dashboard(props: DashboardProps) -> Element {
    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Text content="Enhanced Component System Demo - Frame & Area Access" />
            </Block>

            <Layout direction={Direction::Horizontal}>
                <BasicCard
                    title={"Basic Component".to_string()}
                    content={"This is a standard component without frame/area access.".to_string()}
                />

                <AreaAwareCard
                    title={"Area Access".to_string()}
                    content={"This component can see its rendering area dimensions and position.".to_string()}
                />
            </Layout>

            <Layout direction={Direction::Horizontal}>
                <FrameAwareCard
                    title={"Frame Access".to_string()}
                    content={"This component can access the terminal frame for advanced rendering.".to_string()}
                />

                <FullAccessCard
                    title={"Full Access".to_string()}
                    content={"This component has access to both frame and area for maximum control.".to_string()}
                />
            </Layout>

            <Block
                title={"Instructions".to_string()}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::White)}
            >
                <Text content="• Blue: Basic component (props only)\n• Green: Area-aware component (props + area)\n• Yellow: Frame-aware component (props + frame)\n• Magenta: Full access component (props + frame + area)\n\nPress 'q' to quit | Resize terminal to see area changes" />
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let dashboard_props = DashboardProps {
        title: "Enhanced Component System Demo".to_string(),
    };

    let element = rsx! {
        <Dashboard
            title={dashboard_props.title}
        />
    };

    render(element)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_basic_component() {
        let props = BasicCardProps {
            title: "Test".to_string(),
            content: "Test content".to_string(),
        };
        let _element = BasicCard::create_typed_element(props);
    }

    #[test]
    fn test_area_aware_component() {
        let props = AreaAwareCardProps {
            title: "Test".to_string(),
            content: "Test content".to_string(),
        };
        let _element = AreaAwareCard::create_typed_element(props);
    }

    #[test]
    fn test_frame_aware_component() {
        let props = FrameAwareCardProps {
            title: "Test".to_string(),
            content: "Test content".to_string(),
        };
        let _element = FrameAwareCard::create_typed_element(props);
    }

    #[test]
    fn test_full_access_component() {
        let props = FullAccessCardProps {
            title: "Test".to_string(),
            content: "Test content".to_string(),
        };
        let _element = FullAccessCard::create_typed_element(props);
    }
}
