use crossterm::event::{Event, KeyCode};
use terminus_ui::prelude::*;

/// Props for a clickable button component with callback
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct ClickableButtonProps {
    pub label: String,
    pub on_click: Callback<String>, // Callback that receives a message
    pub variant: String,
    pub is_disabled: bool,
}

/// Props for a counter component with increment/decrement callbacks
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct CounterProps {
    pub initial_value: i32,
    pub on_increment: Callback<i32>, // Callback that receives the new value
    pub on_decrement: Callback<i32>, // Callback that receives the new value
    pub on_reset: Callback<()>,      // Callback with no parameters
}

/// Props for a text input component with change callback
#[derive(Pro<PERSON>, Debug, Clone)]
pub struct TextInputProps {
    pub placeholder: String,
    pub on_change: Callback<String, bool>, // Callback that receives text and returns validation result
    pub on_submit: Callback<String>,       // Callback that receives the final text
}

/// Props for the main app component
#[derive(<PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct CallbackDemoAppProps {
    pub title: String,
}

/// A clickable button component that demonstrates basic callback usage
#[component(ClickableButton)]
fn clickable_button(props: ClickableButtonProps) -> Element {
    let (is_pressed, set_is_pressed) = use_state(false);
    let (last_action, set_last_action) = use_state("Ready".to_string());

    // Handle keyboard events for button interaction
    if let Some(Event::Key(key)) = use_event() {
        if !props.is_disabled {
            match key.code {
                KeyCode::Char('c') | KeyCode::Char('C') => {
                    // Trigger the click callback
                    let click_message =
                        format!("Button '{}' was clicked with 'c' key!", props.label);
                    props.on_click.emit(click_message);

                    // Show visual feedback
                    set_is_pressed.set(true);
                    set_last_action.set("Clicked!".to_string());
                }
                _ => {}
            }
        }
    }

    // Reset pressed state on next render (simple visual feedback)
    if is_pressed.get() {
        set_is_pressed.set(false);
    }

    let border_color = match props.variant.as_str() {
        "primary" => Color::Blue,
        "success" => Color::Green,
        "warning" => Color::Yellow,
        "danger" => Color::Red,
        _ => Color::White,
    };

    let border_color = if props.is_disabled {
        Color::DarkGray
    } else if is_pressed.get() {
        Color::White // Highlight when pressed
    } else {
        border_color
    };

    let current_action = last_action.get();

    rsx! {
        <Block
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),  // Button label
                    Constraint::Length(1),  // Status
                ]}
            >
                <Text content={format!("[{}{}]",
                    props.label,
                    if props.is_disabled { " (disabled)" } else { " (press 'c')" }
                )} />
                <Text content={format!("Status: {}", current_action)} />
            </Layout>
        </Block>
    }
}

/// A counter component that demonstrates multiple callback types
#[component(Counter)]
fn counter(props: CounterProps) -> Element {
    let (count, set_count) = use_state(props.initial_value);
    let (last_action, set_last_action) = use_state("Ready".to_string());
    let current_count = count.get();

    // Handle keyboard events for counter operations
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('i') | KeyCode::Char('I') => {
                // Increment
                let new_value = current_count + 1;
                set_count.set(new_value);
                props.on_increment.emit(new_value);
                set_last_action.set("Incremented!".to_string());
            }
            KeyCode::Char('d') | KeyCode::Char('D') => {
                // Decrement
                let new_value = current_count - 1;
                set_count.set(new_value);
                props.on_decrement.emit(new_value);
                set_last_action.set("Decremented!".to_string());
            }
            KeyCode::Char('r') | KeyCode::Char('R') => {
                // Reset
                set_count.set(props.initial_value);
                props.on_reset.emit(());
                set_last_action.set("Reset!".to_string());
            }
            _ => {}
        }
    }

    let current_action = last_action.get();

    rsx! {
        <Block
            title={"Counter".to_string()}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),  // Counter display
                    Constraint::Length(1),  // Status
                    Constraint::Length(1),  // Increment button
                    Constraint::Length(1),  // Decrement button
                    Constraint::Length(1),  // Reset button
                ]}
            >
                <Text content={format!("Count: {}", current_count)} />
                <Text content={format!("Status: {}", current_action)} />
                <Text content="[+] Increment (press 'i')" />
                <Text content="[-] Decrement (press 'd')" />
                <Text content="[R] Reset (press 'r')" />
            </Layout>
        </Block>
    }
}

/// A text input component that demonstrates callback with return values
#[component(TextInput)]
fn text_input(props: TextInputProps) -> Element {
    let (current_text, set_current_text) = use_state(String::new());
    let (is_valid, set_is_valid) = use_state(true);
    let (last_action, set_last_action) = use_state("Ready".to_string());
    let text = current_text.get();

    // Handle keyboard events for text input operations
    if let Some(Event::Key(key)) = use_event() {
        match key.code {
            KeyCode::Char('t') | KeyCode::Char('T') => {
                // Add text
                let new_text = format!("{}Hello", text);
                set_current_text.set(new_text.clone());
                let valid = props.on_change.emit(new_text);
                set_is_valid.set(valid);
                set_last_action.set("Added text!".to_string());
            }
            KeyCode::Char('s') | KeyCode::Char('S') => {
                // Submit text
                if !text.is_empty() {
                    props.on_submit.emit(text.clone());
                    set_last_action.set("Submitted!".to_string());
                } else {
                    set_last_action.set("Nothing to submit".to_string());
                }
            }
            KeyCode::Backspace => {
                // Remove last character
                if !text.is_empty() {
                    let mut new_text = text.clone();
                    new_text.pop();
                    set_current_text.set(new_text.clone());
                    let valid = props.on_change.emit(new_text);
                    set_is_valid.set(valid);
                    set_last_action.set("Deleted character".to_string());
                } else {
                    set_last_action.set("Nothing to delete".to_string());
                }
            }
            _ => {}
        }
    }

    let border_color = if is_valid.get() {
        Color::Green
    } else {
        Color::Red
    };

    let current_action = last_action.get();

    rsx! {
        <Block
            title={"Text Input".to_string()}
            borders={Borders::ALL}
            border_style={Style::default().fg(border_color)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(1),  // Input display
                    Constraint::Length(1),  // Status
                    Constraint::Length(1),  // Instructions
                    Constraint::Length(1),  // Submit button
                ]}
            >
                <Text content={format!("Input: {}", if text.is_empty() { &props.placeholder } else { &text })} />
                <Text content={format!("Status: {}", current_action)} />
                <Text content="'t' to add text, 's' to submit, Backspace to delete" />
                <Text content={format!("[Submit] {}", if is_valid.get() { "✓" } else { "✗ Invalid" })} />
            </Layout>
        </Block>
    }
}

/// Main app component demonstrating various callback patterns
#[component(CallbackDemoApp)]
fn callback_demo_app(props: CallbackDemoAppProps) -> Element {
    let (messages, set_messages) = use_state(Vec::<String>::new());
    let (_counter_value, set_counter_value) = use_state(0);
    let (_input_text, set_input_text) = use_state(String::new());

    // Callback for button clicks - now using ergonomic syntax!
    let on_button_click = {
        let messages = messages.clone();
        let set_messages = set_messages.clone();
        move |message: String| {
            let mut current_messages = messages.get();
            current_messages.push(message);
            set_messages.set(current_messages);
        }
    };

    // Callbacks for counter - using ergonomic closure syntax!
    let on_increment = {
        let set_counter_value = set_counter_value.clone();
        let messages = messages.clone();
        let set_messages = set_messages.clone();
        move |new_value: i32| {
            set_counter_value.set(new_value);
            let mut current_messages = messages.get();
            current_messages.push(format!("Counter incremented to {}", new_value));
            set_messages.set(current_messages);
        }
    };

    let on_decrement = {
        let set_counter_value = set_counter_value.clone();
        let messages = messages.clone();
        let set_messages = set_messages.clone();
        move |new_value: i32| {
            set_counter_value.set(new_value);
            let mut current_messages = messages.get();
            current_messages.push(format!("Counter decremented to {}", new_value));
            set_messages.set(current_messages);
        }
    };

    let on_reset = {
        let set_counter_value = set_counter_value.clone();
        let messages = messages.clone();
        let set_messages = set_messages.clone();
        move |_: ()| {
            set_counter_value.set(0);
            let mut current_messages = messages.get();
            current_messages.push("Counter reset to 0".to_string());
            set_messages.set(current_messages);
        }
    };

    // Callbacks for text input - demonstrating different callback patterns!
    let on_text_change = move |text: String| {
        // Simple validation: text must be at least 3 characters
        text.len() >= 3
    };

    let on_text_submit = {
        let set_input_text = set_input_text.clone();
        let messages = messages.clone();
        let set_messages = set_messages.clone();
        move |text: String| {
            set_input_text.set(text.clone());
            let mut current_messages = messages.get();
            current_messages.push(format!("Text submitted: '{}'", text));
            set_messages.set(current_messages);
        }
    };

    let message_list = messages.get().join("\n");

    rsx! {
        <Layout direction={Direction::Vertical}>
            // Header
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(1),
                        Constraint::Length(1),
                    ]}
                >
                    <Text content="Enhanced Callback Props + use_event Hook Integration" />
                    <Text content="Closures automatically convert to Callbacks - no more Callback::from()!" />
                </Layout>
            </Block>

            // Main content
            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),  // Left column
                    Constraint::Percentage(50),  // Right column
                ]}
            >
                // Left column - Interactive components
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Percentage(33),  // Button
                        Constraint::Percentage(33),  // Counter
                        Constraint::Percentage(34),  // Text input
                    ]}
                >
                    <ClickableButton
                        label={"Click Me".to_string()}
                        on_click={on_button_click}
                        variant={"primary".to_string()}
                        is_disabled={false}
                    />

                    <Counter
                        initial_value={0}
                        on_increment={on_increment}
                        on_decrement={on_decrement}
                        on_reset={on_reset}
                    />

                    <TextInput
                        placeholder={"Enter text...".to_string()}
                        on_change={on_text_change}
                        on_submit={on_text_submit}
                    />
                </Layout>

                // Right column - Message log
                <Block
                    title={"Event Log".to_string()}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    <Text content={if message_list.is_empty() {
                        "No events yet. Interact with the components on the left!".to_string()
                    } else {
                        message_list
                    }} />
                </Block>
            </Layout>

            // Footer
            <Block
                title={"Interactive Controls".to_string()}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(1),
                        Constraint::Length(1),
                    ]}
                >
                    <Text content="Button: 'c' to click | Counter: 'i' increment, 'd' decrement, 'r' reset | Text: 't' add text, 's' submit, Backspace delete" />
                    <Text content="Press 'q' to quit. This demo shows callback props + use_event hook working together!" />
                </Layout>
            </Block>
        </Layout>
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let app_props = CallbackDemoAppProps {
        title: "Callback Props Demo".to_string(),
    };

    let element = rsx! {
        <CallbackDemoApp title={app_props.title} />
    };

    render(element)
}
