[package]
name = "terminus_ui_core"
version = "0.1.0"
edition = "2024"

[dependencies]
better-panic = { workspace = true }
crossbeam-channel = { workspace = true }
crossterm = { workspace = true }
human-panic = { workspace = true }
lazy_static = { workspace = true }
once_cell = { workspace = true }
parking_lot = { workspace = true }
ratatui = { workspace = true, features = ["all-widgets"] }
reqwest = { workspace = true, features = ["json"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
tokio = { workspace = true }
tracing = { workspace = true }
tracing-appender = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
