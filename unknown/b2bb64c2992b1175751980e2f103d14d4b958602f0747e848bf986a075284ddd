use std::fmt;
use std::sync::Arc;

/// Universal callback wrapper inspired by <PERSON><PERSON>'s Callback system.
///
/// This provides a type-safe way to pass function callbacks between components,
/// similar to React's event handlers and <PERSON><PERSON>'s callback system.
///
/// # Examples
///
/// ```rust
/// use terminus_ui::prelude::*;
///
/// // Define a callback that takes a string and returns nothing
/// let on_click: Callback<String> = Callback::from(|msg: String| {
///     println!("Clicked: {}", msg);
/// });
///
/// // Emit the callback
/// on_click.emit("Hello".to_string());
///
/// // Define a callback with return value
/// let transform: Callback<i32, String> = Callback::from(|num: i32| {
///     format!("Number: {}", num)
/// });
///
/// let result = transform.emit(42);
/// assert_eq!(result, "Number: 42");
/// ```
#[derive(Clone)]
pub struct Callback<IN, OUT = ()> {
    callback: Arc<dyn Fn(IN) -> OUT + Send + Sync>,
}

impl<IN, OUT> Callback<IN, OUT> {
    /// Create a new callback from a function
    pub fn new<F>(func: F) -> Self
    where
        F: Fn(IN) -> OUT + Send + Sync + 'static,
    {
        Self {
            callback: Arc::new(func),
        }
    }

    /// Emit the callback with the given input value
    pub fn emit(&self, input: IN) -> OUT {
        (self.callback)(input)
    }

    /// Create a reformed callback that transforms the input before calling this callback
    ///
    /// # Examples
    ///
    /// ```rust
    /// use terminus_ui::prelude::*;
    ///
    /// let original: Callback<String> = Callback::from(|s: String| {
    ///     println!("Got: {}", s);
    /// });
    ///
    /// let reformed: Callback<i32> = original.reform(|num: i32| format!("Number: {}", num));
    /// reformed.emit(42); // Prints "Got: Number: 42"
    /// ```
    pub fn reform<F, T>(&self, func: F) -> Callback<T, OUT>
    where
        F: Fn(T) -> IN + Send + Sync + 'static,
        IN: 'static,
        OUT: 'static,
    {
        let callback = self.callback.clone();
        Callback::new(move |input: T| {
            let transformed = func(input);
            callback(transformed)
        })
    }

    /// Create a reformed callback that optionally transforms the input
    /// Only calls the original callback if the transform function returns Some
    ///
    /// # Examples
    ///
    /// ```rust
    /// use terminus_ui::prelude::*;
    ///
    /// let original: Callback<String> = Callback::from(|s: String| {
    ///     println!("Got: {}", s);
    /// });
    ///
    /// let filtered: Callback<i32, Option<()>> = original.filter_reform(|num: i32| {
    ///     if num > 0 {
    ///         Some(format!("Positive: {}", num))
    ///     } else {
    ///         None
    ///     }
    /// });
    ///
    /// filtered.emit(42);  // Prints "Got: Positive: 42", returns Some(())
    /// filtered.emit(-1);  // Does nothing, returns None
    /// ```
    pub fn filter_reform<F, T>(&self, func: F) -> Callback<T, Option<OUT>>
    where
        F: Fn(T) -> Option<IN> + Send + Sync + 'static,
        IN: 'static,
        OUT: 'static,
    {
        let callback = self.callback.clone();
        Callback::new(move |input: T| func(input).map(|v| callback(v)))
    }
}

impl<IN> Callback<IN> {
    /// Create a no-op callback that does nothing when called
    /// Useful for optional callbacks or default values
    pub fn noop() -> Self {
        Self::new(|_| {})
    }
}

impl<IN, OUT> Default for Callback<IN, OUT>
where
    OUT: Default,
{
    fn default() -> Self {
        Self::new(|_| OUT::default())
    }
}

impl<IN, OUT, F> From<F> for Callback<IN, OUT>
where
    F: Fn(IN) -> OUT + Send + Sync + 'static,
{
    fn from(func: F) -> Self {
        Self::new(func)
    }
}

impl<IN, OUT> fmt::Debug for Callback<IN, OUT> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Callback")
            .field("callback", &"<function>")
            .finish()
    }
}

impl<IN, OUT> PartialEq for Callback<IN, OUT> {
    fn eq(&self, other: &Self) -> bool {
        // Compare by pointer equality since we can't compare function contents
        Arc::ptr_eq(&self.callback, &other.callback)
    }
}

/// Trait for types that can be converted into event callbacks
/// This allows for flexible callback prop handling in components
pub trait IntoCallback<IN, OUT = ()> {
    fn into_callback(self) -> Callback<IN, OUT>;
}

impl<IN, OUT, F> IntoCallback<IN, OUT> for F
where
    F: Fn(IN) -> OUT + Send + Sync + 'static,
{
    fn into_callback(self) -> Callback<IN, OUT> {
        Callback::from(self)
    }
}

impl<IN, OUT> IntoCallback<IN, OUT> for Callback<IN, OUT> {
    fn into_callback(self) -> Callback<IN, OUT> {
        self
    }
}

/// Trait for converting callbacks into props that can be either Option<Callback> or Callback
/// This handles the automatic wrapping for optional callback props in the rsx! macro
pub trait IntoCallbackProp<T> {
    fn into_callback_prop(self) -> T;
}

// Implementation for non-optional callback props (Callback<IN, OUT>)
impl<F, IN, OUT> IntoCallbackProp<Callback<IN, OUT>> for F
where
    F: IntoCallback<IN, OUT>,
{
    fn into_callback_prop(self) -> Callback<IN, OUT> {
        self.into_callback()
    }
}

// Implementation for optional callback props (Option<Callback<IN, OUT>>)
impl<F, IN, OUT> IntoCallbackProp<Option<Callback<IN, OUT>>> for F
where
    F: IntoCallback<IN, OUT>,
{
    fn into_callback_prop(self) -> Option<Callback<IN, OUT>> {
        Some(self.into_callback())
    }
}

/// Trait for converting props into optional props that can be either Option<T> or T
/// This handles the automatic wrapping for optional props in the rsx! macro
pub trait IntoOptionalProp<T> {
    fn into_optional_prop(self) -> T;
}

// Implementation for optional props (Option<T>) - wrap T in Some(T)
impl<T> IntoOptionalProp<Option<T>> for T
where
    T: 'static,
{
    fn into_optional_prop(self) -> Option<T> {
        Some(self)
    }
}

// Implementation for when we already have an Option<T>
impl<T> IntoOptionalProp<Option<T>> for Option<T>
where
    T: 'static,
{
    fn into_optional_prop(self) -> Option<T> {
        self
    }
}

/// Convenience macro for creating callbacks with better type inference
///
/// This macro helps with type inference when creating callbacks from closures
///
/// # Examples
///
/// ```rust
/// use terminus_ui::prelude::*;
///
/// // Instead of: Callback::from(|x: i32| println!("{}", x))
/// let callback = callback!(|x: i32| println!("{}", x));
///
/// // With return type
/// let callback = callback!(|x: i32| -> String { format!("{}", x) });
/// ```
#[macro_export]
macro_rules! callback {
    // Single parameter callback
    (|$param:ident: $param_type:ty| $body:expr) => {
        $crate::Callback::from(move |$param: $param_type| $body)
    };
    // Single parameter callback with explicit return type
    (|$param:ident: $param_type:ty| -> $ret:ty { $($body:tt)* }) => {
        $crate::Callback::<$param_type, $ret>::from(move |$param: $param_type| { $($body)* })
    };
    // Multiple parameter callback (using tuples)
    (|$($param:ident: $param_type:ty),+| $body:expr) => {
        $crate::Callback::from(move |($($param,)*): ($($param_type,)*)| $body)
    };
    // Multiple parameter callback with explicit return type
    (|$($param:ident: $param_type:ty),+| -> $ret:ty { $($body:tt)* }) => {
        $crate::Callback::<($($param_type,)*), $ret>::from(move |($($param,)*): ($($param_type,)*)| { $($body)* })
    };
}

impl<IN, OUT> Callback<IN, OUT> {
    /// Create a callback from a closure with explicit type annotation
    /// This helps with type inference in complex scenarios
    pub fn from_fn<F>(func: F) -> Self
    where
        F: Fn(IN) -> OUT + Send + Sync + 'static,
    {
        Self::from(func)
    }

    /// Create a callback that ignores its input and returns a constant value
    pub fn constant(value: OUT) -> Self
    where
        OUT: Clone + Send + Sync + 'static,
    {
        Self::from(move |_| value.clone())
    }

    /// Create a callback that just prints its input (useful for debugging)
    pub fn debug() -> Self
    where
        IN: std::fmt::Debug,
        OUT: Default,
    {
        Self::from(|input| {
            println!("Callback called with: {:?}", input);
            OUT::default()
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::{Arc, Mutex};

    #[test]
    fn test_callback_basic() {
        let result = Arc::new(Mutex::new(String::new()));
        let result_clone = result.clone();

        let callback: Callback<String> = Callback::from(move |msg: String| {
            *result_clone.lock().unwrap() = msg;
        });

        callback.emit("Hello".to_string());
        assert_eq!(*result.lock().unwrap(), "Hello");
    }

    #[test]
    fn test_callback_with_return() {
        let callback: Callback<i32, String> = Callback::from(|num: i32| format!("Number: {}", num));

        let result = callback.emit(42);
        assert_eq!(result, "Number: 42");
    }

    #[test]
    fn test_callback_reform() {
        let result = Arc::new(Mutex::new(String::new()));
        let result_clone = result.clone();

        let original: Callback<String> = Callback::from(move |msg: String| {
            *result_clone.lock().unwrap() = msg;
        });

        let reformed: Callback<i32> = original.reform(|num: i32| format!("Number: {}", num));
        reformed.emit(42);

        assert_eq!(*result.lock().unwrap(), "Number: 42");
    }

    #[test]
    fn test_callback_filter_reform() {
        let result = Arc::new(Mutex::new(String::new()));
        let result_clone = result.clone();

        let original: Callback<String> = Callback::from(move |msg: String| {
            *result_clone.lock().unwrap() = msg;
        });

        let filtered: Callback<i32, Option<()>> = original.filter_reform(|num: i32| {
            if num > 0 {
                Some(format!("Positive: {}", num))
            } else {
                None
            }
        });

        let result1 = filtered.emit(42);
        assert_eq!(*result.lock().unwrap(), "Positive: 42");
        assert!(result1.is_some());

        result.lock().unwrap().clear();
        let result2 = filtered.emit(-1);
        assert_eq!(*result.lock().unwrap(), "");
        assert!(result2.is_none());
    }

    #[test]
    fn test_callback_noop() {
        let callback: Callback<String> = Callback::noop();
        // Should not panic
        callback.emit("test".to_string());
    }

    #[test]
    fn test_callback_equality() {
        let func = |_: i32| {};
        let callback1: Callback<i32> = Callback::from(func);
        let callback2 = callback1.clone();
        let callback3: Callback<i32> = Callback::from(|_: i32| {});

        assert_eq!(callback1, callback2);
        assert_ne!(callback1, callback3);
    }

    #[test]
    fn test_into_callback_trait() {
        use crate::IntoCallback;

        // Test that closures can be converted via IntoCallback
        let closure = |x: i32| x * 2;
        let callback: Callback<i32, i32> = closure.into_callback();

        assert_eq!(callback.emit(5), 10);
    }

    #[test]
    fn test_callback_from_fn() {
        let callback = Callback::from_fn(|x: String| x.len());
        assert_eq!(callback.emit("hello".to_string()), 5);
    }

    #[test]
    fn test_callback_constant() {
        let callback: Callback<i32, String> = Callback::constant("always this".to_string());
        assert_eq!(callback.emit(42), "always this");
        assert_eq!(callback.emit(100), "always this");
    }

    #[test]
    fn test_callback_macro() {
        // Test the callback! macro
        let callback = callback!(|x: i32| x + 1);
        assert_eq!(callback.emit(5), 6);

        // Test with explicit return type
        let callback = callback!(|x: i32| -> String { format!("Value: {}", x) });
        assert_eq!(callback.emit(42), "Value: 42");
    }
}
