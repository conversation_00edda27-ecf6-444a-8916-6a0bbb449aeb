use crate::parsing::{RsxElement, RsxProp, RsxPropValue};

/// RSX structure analysis for optimization and code generation
pub struct RsxAnalyzer;

impl RsxAnalyzer {
    /// Analyze RSX element structure for optimization opportunities
    pub fn analyze_element(element: &RsxElement) -> ElementAnalysis {
        match element {
            RsxElement::Element {
                name,
                props,
                children,
                ..
            } => {
                let element_name = name.segments.last().unwrap().ident.to_string();
                let complexity = Self::analyze_complexity(props, children);
                let optimization_hints =
                    Self::generate_optimization_hints(&element_name, props, children);
                let prop_analysis = Self::analyze_props(props);
                let children_analysis = Self::analyze_children(children);

                ElementAnalysis {
                    element_type: ElementType::Component(element_name),
                    complexity,
                    optimization_hints,
                    prop_analysis,
                    children_analysis,
                    has_dynamic_content: Self::has_dynamic_content(props, children),
                    is_self_closing: children.is_empty(),
                    nesting_depth: Self::calculate_nesting_depth(children),
                }
            }
            RsxElement::Text { content, .. } => ElementAnalysis {
                element_type: ElementType::Text(content.clone()),
                complexity: ComplexityLevel::Simple,
                optimization_hints: vec![],
                prop_analysis: PropAnalysis::default(),
                children_analysis: ChildrenAnalysis::default(),
                has_dynamic_content: false,
                is_self_closing: true,
                nesting_depth: 0,
            },
            RsxElement::Expression { .. } => ElementAnalysis {
                element_type: ElementType::Expression,
                complexity: ComplexityLevel::Dynamic,
                optimization_hints: vec![
                    "Consider caching expression result if expensive".to_string(),
                ],
                prop_analysis: PropAnalysis::default(),
                children_analysis: ChildrenAnalysis::default(),
                has_dynamic_content: true,
                is_self_closing: true,
                nesting_depth: 0,
            },
        }
    }

    /// Analyze complexity of an element
    fn analyze_complexity(props: &[RsxProp], children: &[RsxElement]) -> ComplexityLevel {
        let prop_complexity = props
            .iter()
            .map(|p| Self::prop_complexity(&p.value))
            .max()
            .unwrap_or(ComplexityLevel::Simple);

        let children_complexity = children
            .iter()
            .map(|c| Self::analyze_element(c).complexity)
            .max()
            .unwrap_or(ComplexityLevel::Simple);

        // Return the highest complexity level
        match (prop_complexity, children_complexity) {
            (ComplexityLevel::Dynamic, _) | (_, ComplexityLevel::Dynamic) => {
                ComplexityLevel::Dynamic
            }
            (ComplexityLevel::Moderate, _) | (_, ComplexityLevel::Moderate) => {
                ComplexityLevel::Moderate
            }
            _ => ComplexityLevel::Simple,
        }
    }

    /// Analyze prop complexity
    fn prop_complexity(value: &RsxPropValue) -> ComplexityLevel {
        match value {
            RsxPropValue::Literal { .. } => ComplexityLevel::Simple,
            RsxPropValue::Expression { .. } => ComplexityLevel::Dynamic,
        }
    }

    /// Generate optimization hints
    fn generate_optimization_hints(
        element_name: &str,
        props: &[RsxProp],
        children: &[RsxElement],
    ) -> Vec<String> {
        let mut hints = Vec::new();

        // Check for expensive operations
        if props.len() > 10 {
            hints.push("Consider reducing number of props for better performance".to_string());
        }

        if children.len() > 20 {
            hints.push("Consider virtualizing large lists of children".to_string());
        }

        // Element-specific hints
        if element_name == "Text"
            && props
                .iter()
                .any(|p| p.name == "content" && matches!(p.value, RsxPropValue::Expression { .. }))
        {
            // hints.push("Consider memoizing dynamic text content".to_string());
        }

        hints
    }

    /// Analyze props structure
    fn analyze_props(props: &[RsxProp]) -> PropAnalysis {
        let total_props = props.len();
        let literal_props = props
            .iter()
            .filter(|p| matches!(p.value, RsxPropValue::Literal { .. }))
            .count();
        let expression_props = props
            .iter()
            .filter(|p| matches!(p.value, RsxPropValue::Expression { .. }))
            .count();

        let prop_names: Vec<proc_macro2::Ident> = props.iter().map(|p| p.name.clone()).collect();
        let has_event_handlers = prop_names
            .iter()
            .any(|name| name.to_string().starts_with("on"));
        let has_style_props = prop_names
            .iter()
            .any(|name| name == "style" || name == "class");

        PropAnalysis {
            total_props,
            literal_props,
            expression_props,
            prop_names,
            has_event_handlers,
            has_style_props,
        }
    }

    /// Analyze children structure
    fn analyze_children(children: &[RsxElement]) -> ChildrenAnalysis {
        let total_children = children.len();
        let text_children = children
            .iter()
            .filter(|c| matches!(c, RsxElement::Text { .. }))
            .count();
        let element_children = children
            .iter()
            .filter(|c| matches!(c, RsxElement::Element { .. }))
            .count();
        let expression_children = children
            .iter()
            .filter(|c| matches!(c, RsxElement::Expression { .. }))
            .count();

        let max_nesting_depth = children
            .iter()
            .map(|c| Self::calculate_nesting_depth(&[c.clone()]))
            .max()
            .unwrap_or(0);

        ChildrenAnalysis {
            total_children,
            text_children,
            element_children,
            expression_children,
            max_nesting_depth,
        }
    }

    /// Check if element has dynamic content
    fn has_dynamic_content(props: &[RsxProp], children: &[RsxElement]) -> bool {
        // Check props for expressions
        let has_dynamic_props = props
            .iter()
            .any(|p| matches!(p.value, RsxPropValue::Expression { .. }));

        // Check children for expressions or dynamic elements
        let has_dynamic_children = children.iter().any(|c| {
            matches!(c, RsxElement::Expression { .. })
                || (matches!(c, RsxElement::Element { .. })
                    && Self::analyze_element(c).has_dynamic_content)
        });

        has_dynamic_props || has_dynamic_children
    }

    /// Calculate nesting depth
    fn calculate_nesting_depth(children: &[RsxElement]) -> usize {
        children
            .iter()
            .map(|child| match child {
                RsxElement::Element { children, .. } => 1 + Self::calculate_nesting_depth(children),
                _ => 1,
            })
            .max()
            .unwrap_or(0)
    }

    /// Analyze entire RSX tree for global optimizations
    pub fn analyze_tree(root: &RsxElement) -> TreeAnalysis {
        let mut total_elements = 0;
        let mut max_depth = 0;
        let mut dynamic_elements = 0;
        let mut optimization_opportunities = Vec::new();

        Self::analyze_tree_recursive(
            root,
            0,
            &mut total_elements,
            &mut max_depth,
            &mut dynamic_elements,
            &mut optimization_opportunities,
        );

        TreeAnalysis {
            total_elements,
            max_depth,
            dynamic_elements,
            optimization_opportunities,
            complexity_score: Self::calculate_complexity_score(
                total_elements,
                max_depth,
                dynamic_elements,
            ),
        }
    }

    /// Recursive tree analysis helper
    fn analyze_tree_recursive(
        element: &RsxElement,
        current_depth: usize,
        total_elements: &mut usize,
        max_depth: &mut usize,
        dynamic_elements: &mut usize,
        optimization_opportunities: &mut Vec<String>,
    ) {
        *total_elements += 1;
        *max_depth = (*max_depth).max(current_depth);

        let analysis = Self::analyze_element(element);
        if analysis.has_dynamic_content {
            *dynamic_elements += 1;
        }

        optimization_opportunities.extend(analysis.optimization_hints);

        if let RsxElement::Element { children, .. } = element {
            for child in children {
                Self::analyze_tree_recursive(
                    child,
                    current_depth + 1,
                    total_elements,
                    max_depth,
                    dynamic_elements,
                    optimization_opportunities,
                );
            }
        }
    }

    /// Calculate complexity score for the entire tree
    fn calculate_complexity_score(
        total_elements: usize,
        max_depth: usize,
        dynamic_elements: usize,
    ) -> f64 {
        // Simple scoring algorithm
        let base_score = total_elements as f64;
        let depth_penalty = (max_depth as f64).powi(2) * 0.1;
        let dynamic_penalty = dynamic_elements as f64 * 2.0;

        base_score + depth_penalty + dynamic_penalty
    }
}

/// Analysis result for a single element
#[derive(Debug, Clone)]
pub struct ElementAnalysis {
    pub element_type: ElementType,
    pub complexity: ComplexityLevel,
    pub optimization_hints: Vec<String>,
    pub prop_analysis: PropAnalysis,
    pub children_analysis: ChildrenAnalysis,
    pub has_dynamic_content: bool,
    pub is_self_closing: bool,
    pub nesting_depth: usize,
}

/// Type of RSX element
#[derive(Debug, Clone)]
pub enum ElementType {
    Component(String),
    Text(String),
    Expression,
}

/// Complexity level of an element
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum ComplexityLevel {
    Simple,
    Moderate,
    Dynamic,
}

/// Analysis of props
#[derive(Debug, Clone, Default)]
pub struct PropAnalysis {
    pub total_props: usize,
    pub literal_props: usize,
    pub expression_props: usize,
    pub prop_names: Vec<proc_macro2::Ident>,
    pub has_event_handlers: bool,
    pub has_style_props: bool,
}

/// Analysis of children
#[derive(Debug, Clone, Default)]
pub struct ChildrenAnalysis {
    pub total_children: usize,
    pub text_children: usize,
    pub element_children: usize,
    pub expression_children: usize,
    pub max_nesting_depth: usize,
}

/// Analysis of entire RSX tree
#[derive(Debug, Clone)]
pub struct TreeAnalysis {
    pub total_elements: usize,
    pub max_depth: usize,
    pub dynamic_elements: usize,
    pub optimization_opportunities: Vec<String>,
    pub complexity_score: f64,
}

/// Context for RSX code generation
#[derive(Debug, Clone)]
pub struct GenerationContext {
    pub element_analysis: ElementAnalysis,
    pub tree_analysis: TreeAnalysis,
    pub optimization_level: OptimizationLevel,
    // pub target_features: Vec<String>,
}

/// Optimization level for code generation
#[derive(Debug, Clone)]
pub enum OptimizationLevel {
    Debug,
    Release,
    Aggressive,
}

impl GenerationContext {
    /// Create a new generation context
    pub fn new(element: &RsxElement, optimization_level: OptimizationLevel) -> Self {
        let element_analysis = RsxAnalyzer::analyze_element(element);
        let tree_analysis = RsxAnalyzer::analyze_tree(element);

        Self {
            element_analysis,
            tree_analysis,
            optimization_level,
            // target_features: vec![],
        }
    }

    /// Check if optimization should be applied
    pub fn should_optimize(&self, optimization_name: &str) -> bool {
        match self.optimization_level {
            OptimizationLevel::Debug => false,
            OptimizationLevel::Release => {
                // Apply basic optimizations
                matches!(optimization_name, "inline_simple" | "cache_static")
            }
            OptimizationLevel::Aggressive => {
                // Apply all optimizations
                true
            }
        }
    }

    /// Get optimization hints for code generation
    pub fn get_optimization_hints(&self) -> &[String] {
        &self.element_analysis.optimization_hints
    }
}
