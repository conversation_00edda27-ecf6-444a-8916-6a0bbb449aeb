use std::time::{Duration, SystemTime, UNIX_EPOCH};
use terminus_ui::prelude::*;

/// Example demonstrating React-style useEffect and useAsyncEffect hook usage
/// This showcases all the key features of both sync and async effect implementations

// Sync Timer component demonstrating useEffect with cleanup
#[derive(<PERSON><PERSON>, Debug, Clone)]
struct SyncTimerProps {
    pub title: String,
}

#[component(SyncTimer)]
fn sync_timer(props: SyncTimerProps) -> Element {
    let (seconds, set_seconds) = use_state(0);
    let (is_running, _set_is_running) = use_state(true);

    // Synchronous effect with cleanup - runs once on mount
    let set_seconds_clone = set_seconds.clone();
    use_effect(
        move || {
            let set_seconds_inner = set_seconds_clone.clone();

            // Start timer in background thread (sync approach)
            let handle = std::thread::spawn(move || {
                for i in 1..=60 {
                    std::thread::sleep(Duration::from_secs(1));
                    set_seconds_inner.set(i);
                }
            });

            // Return cleanup function
            Some(move || {
                // In a real implementation, we'd have a way to stop the thread
                // For demo purposes, we'll just drop the handle
                drop(handle);
            })
        },
        (), // Empty deps = run once on mount
    );

    let seconds_value = seconds.get();
    let is_running_value = is_running.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Green)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("⏱️  Sync Timer: {} seconds", seconds_value)} />
                <Text content={format!("▶️  Status: {}", if is_running_value { "Running" } else { "Stopped" })} />
                <Text content="This demonstrates synchronous useEffect with cleanup" />
            </Layout>
        </Block>
    }
}

// Async Timer component demonstrating useAsyncEffect with cleanup
#[derive(Props, Debug, Clone)]
struct AsyncTimerProps {
    pub title: String,
}

#[component(AsyncTimer)]
fn async_timer(props: AsyncTimerProps) -> Element {
    let (seconds, set_seconds) = use_state(0);
    let (is_running, _set_is_running) = use_state(true);

    // Asynchronous effect with cleanup - runs once on mount
    let set_seconds_clone = set_seconds.clone();
    use_async_effect(
        move || {
            let set_seconds_inner = set_seconds_clone.clone();
            async move {
                // Start async timer
                let handle = tokio::spawn(async move {
                    for i in 1..=60 {
                        tokio::time::sleep(Duration::from_secs(1)).await;
                        set_seconds_inner.set(i);
                    }
                });

                // Return async cleanup function
                Some(move || async move {
                    handle.abort(); // Cancel the async task
                })
            }
        },
        (), // Empty deps = run once on mount
    );

    let seconds_value = seconds.get();
    let is_running_value = is_running.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("⏱️  Async Timer: {} seconds", seconds_value)} />
                <Text content={format!("▶️  Status: {}", if is_running_value { "Running" } else { "Stopped" })} />
                <Text content="This demonstrates asynchronous useAsyncEffect with cleanup" />
            </Layout>
        </Block>
    }
}

// Data fetcher component demonstrating dependency-based effects
#[derive(Props, Debug, Clone)]
struct DataFetcherProps {
    pub title: String,
    pub user_id: u32,
}

#[component(DataFetcher)]
fn data_fetcher(props: DataFetcherProps) -> Element {
    let (data, set_data) = use_state("Loading...".to_string());
    let (fetch_count, set_fetch_count) = use_state(0);

    // Effect that runs when user_id changes
    let set_data_clone = set_data.clone();
    let set_fetch_count_clone = set_fetch_count.clone();
    let user_id = props.user_id;

    use_effect(
        move || {
            // Simulate data fetching
            let fake_data = format!(
                "User data for ID: {} (timestamp: {})",
                user_id,
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs()
            );

            set_data_clone.set(fake_data);
            set_fetch_count_clone.update(|prev| prev + 1);

            None::<Box<dyn FnOnce() + Send>> // No cleanup needed
        },
        user_id, // Re-run when user_id changes
    );

    let data_value = data.get();
    let fetch_count_value = fetch_count.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Blue)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("👤 User ID: {}", props.user_id)} />
                <Text content={format!("📊 Data: {}", data_value)} />
                <Text content={format!("🔄 Fetch Count: {}", fetch_count_value)} />
                <Text content="This demonstrates dependency-based effects" />
            </Layout>
        </Block>
    }
}

// Async Data fetcher component demonstrating dependency-based async effects
#[derive(Props, Debug, Clone)]
struct AsyncDataFetcherProps {
    pub title: String,
    pub user_id: u32,
}

#[component(AsyncDataFetcher)]
fn async_data_fetcher(props: AsyncDataFetcherProps) -> Element {
    let (data, set_data) = use_state("Loading...".to_string());
    let (fetch_count, set_fetch_count) = use_state(0);

    // Async effect that runs when user_id changes
    let set_data_clone = set_data.clone();
    let set_fetch_count_clone = set_fetch_count.clone();
    let user_id = props.user_id;

    use_async_effect(
        move || {
            let set_data_inner = set_data_clone.clone();
            let set_fetch_count_inner = set_fetch_count_clone.clone();
            async move {
                // Simulate async data fetching with delay
                tokio::time::sleep(Duration::from_millis(500)).await;

                let fake_data = format!(
                    "Async User data for ID: {} (timestamp: {})",
                    user_id,
                    SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs()
                );

                set_data_inner.set(fake_data);
                set_fetch_count_inner.update(|prev| prev + 1);

                None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>> // No cleanup needed
            }
        },
        user_id, // Re-run when user_id changes
    );

    let data_value = data.get();
    let fetch_count_value = fetch_count.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Magenta)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("👤 User ID: {}", props.user_id)} />
                <Text content={format!("📊 Data: {}", data_value)} />
                <Text content={format!("🔄 Fetch Count: {}", fetch_count_value)} />
                <Text content="This demonstrates async dependency-based effects" />
            </Layout>
        </Block>
    }
}

// Logger component demonstrating effects that run on every render
#[derive(Props, Debug, Clone)]
struct LoggerProps {
    pub title: String,
}

#[component(Logger)]
fn logger(props: LoggerProps) -> Element {
    let (render_count, set_render_count) = use_state(0);
    let (log_entries, set_log_entries) = use_state(Vec::<String>::new());

    // Effect with no dependencies - runs on every render
    let set_log_entries_clone = set_log_entries.clone();
    let render_count_value = render_count.get();

    use_effect::<i32, _, _>(
        move || {
            let timestamp = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();

            let log_entry = format!("Render #{} at {}", render_count_value, timestamp);

            set_log_entries_clone.update(|mut prev| {
                prev.push(log_entry);
                if prev.len() > 5 {
                    prev.remove(0); // Keep only last 5 entries
                }
                prev
            });

            None::<Box<dyn FnOnce() + Send>>
        },
        None, // No dependencies = run on every render
    );

    // Update render count
    set_render_count.update(|prev| prev + 1);

    let log_entries_value = log_entries.get();

    rsx! {
        <Block
            title={props.title}
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Yellow)}
        >
            <Layout direction={Direction::Vertical}>
                <Text content={format!("🔢 Render Count: {}", render_count_value)} />
                <Text content="📝 Recent Log Entries:" />
                {log_entries_value.iter().map(|entry| {
                    rsx! { <Text content={format!("  • {}", entry)} /> }
                }).collect::<Vec<_>>()}
                <Text content="This effect runs on every render (no dependencies)" />
            </Layout>
        </Block>
    }
}

// Main application component
#[derive(Props, Debug, Clone)]
struct UseEffectAppProps {
    pub title: String,
}

#[component(UseEffectApp)]
fn use_effect_app(props: UseEffectAppProps) -> Element {
    let (current_user_id, _set_current_user_id) = use_state(1u32);
    let (demo_counter, _set_demo_counter) = use_state(0u32);

    // Effect that demonstrates tuple dependencies
    let current_user_id_value = current_user_id.get();
    let demo_counter_value = demo_counter.get();

    use_effect(
        move || {
            println!(
                "User ID or demo counter changed: user={}, counter={}",
                current_user_id_value, demo_counter_value
            );
            None::<Box<dyn FnOnce() + Send>>
        },
        (current_user_id_value, demo_counter_value), // Tuple dependency
    );

    rsx! {
        <Layout direction={Direction::Vertical}>
            <Block
                title={props.title}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="🚀 React-style useEffect & useAsyncEffect Hook Demo" />
                    <Text content={format!("👤 Current User ID: {}", current_user_id_value)} />
                    <Text content={format!("🔢 Demo Counter: {}", demo_counter_value)} />
                    <Text content="This demo showcases both sync and async effect patterns" />
                </Layout>
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(25),
                    Constraint::Percentage(25),
                    Constraint::Percentage(25),
                    Constraint::Percentage(25),
                ]}
            >
                <SyncTimer title={"⏱️  Sync Timer".to_string()} />

                <AsyncTimer title={"🚀 Async Timer".to_string()} />

                <DataFetcher
                    title={"📊 Sync Data".to_string()}
                    user_id={current_user_id_value}
                />

                <AsyncDataFetcher
                    title={"🚀 Async Data".to_string()}
                    user_id={current_user_id_value}
                />
            </Layout>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(100),
                ]}
            >
                <Logger title={"📝 Render Logger".to_string()} />
            </Layout>

            <Block
                title="📋 useEffect & useAsyncEffect Features Demonstrated"
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan)}
            >
                <Layout direction={Direction::Vertical}>
                    <Text content="✅ Sync Effects (useEffect) - Traditional synchronous side effects" />
                    <Text content="✅ Async Effects (useAsyncEffect) - Native async/await support" />
                    <Text content="✅ Empty dependencies [] - Effects run once on mount" />
                    <Text content="✅ Single dependency - Effects run when dependencies change" />
                    <Text content="✅ No dependencies - Effects run on every render" />
                    <Text content="✅ Tuple dependencies - Effects run when any tuple element changes" />
                    <Text content="✅ Cleanup functions - Both sync and async cleanup support" />
                    <Text content="✅ Effect execution order - Multiple effects run in declaration order" />
                    <Text content="✅ React parity - Exact useEffect behavior with Rust safety" />
                    <Text content="✅ Thread safety - All effects work correctly in concurrent contexts" />
                </Layout>
            </Block>
        </Layout>
    }
}

// Main function
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    println!("🚀 Starting useEffect & useAsyncEffect Hook Demo...");

    let app_props = UseEffectAppProps {
        title: "🔄 useEffect & useAsyncEffect Demo - Terminus UI".to_string(),
    };

    let element = rsx! {
        <UseEffectApp title={app_props.title} />
    };

    println!("🎯 Running comprehensive effect demo with both sync and async patterns...");
    println!("📝 This demo showcases React-style effect behavior:");
    println!("   - Synchronous effects (useEffect) for immediate side effects");
    println!("   - Asynchronous effects (useAsyncEffect) for async operations");
    println!("   - Effects with different dependency patterns");
    println!("   - Both sync and async cleanup function management");
    println!("   - Effect execution order and timing");
    println!("   - Integration with useState hooks");

    // Run the UI
    render_async(element).await?;

    println!("✅ useEffect & useAsyncEffect demo completed!");
    Ok(())
}
