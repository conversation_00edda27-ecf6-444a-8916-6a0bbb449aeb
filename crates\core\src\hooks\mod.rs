mod context;
mod effect;
mod event;
mod future;
mod state;

pub use context::{
    Context, clear_context_providers, create_context_with_default, use_context,
    use_context_provider, use_context_with_default,
};
pub use effect::{AsyncCleanupFn, CleanupFn, EffectDependencies, use_async_effect, use_effect};
pub use event::{set_current_event_context, use_event};
pub use future::{FutureHandle, FutureState, use_future};
pub use state::{StateHandle, StateSetter, use_state};

use std::any::Any;
use std::cell::RefCell;
use std::collections::HashMap;
use std::rc::Rc;

/// A hook context that manages state for components
pub struct HookContext {
    states: RefCell<HashMap<usize, Box<dyn Any>>>,
    current_hook: RefCell<usize>,
}

impl HookContext {
    pub fn new() -> Self {
        Self {
            states: RefCell::new(HashMap::new()),
            current_hook: RefCell::new(0),
        }
    }

    /// Reset the hook counter for a new render cycle
    pub fn reset(&self) {
        *self.current_hook.borrow_mut() = 0;
    }

    /// Get the next hook index
    fn next_hook_index(&self) -> usize {
        let mut current = self.current_hook.borrow_mut();
        let index = *current;
        *current += 1;
        index
    }

    /// Get or initialize state for a hook
    pub fn get_or_init_state<T: 'static>(&self, init: impl FnOnce() -> T) -> Rc<RefCell<T>> {
        let index = self.next_hook_index();
        let mut states = self.states.borrow_mut();

        if let Some(state) = states.get(&index) {
            if let Some(typed_state) = state.downcast_ref::<Rc<RefCell<T>>>() {
                return typed_state.clone();
            }
        }

        let new_state = Rc::new(RefCell::new(init()));
        states.insert(index, Box::new(new_state.clone()));
        new_state
    }
}

impl Default for HookContext {
    fn default() -> Self {
        Self::new()
    }
}

thread_local! {
    static HOOK_CONTEXT: RefCell<Option<Rc<HookContext>>> = const { RefCell::new(None) };
}

/// Set the current hook context
pub fn set_hook_context(context: Rc<HookContext>) {
    HOOK_CONTEXT.with(|ctx| {
        *ctx.borrow_mut() = Some(context);
    });
}

/// Clear the current hook context
pub fn clear_hook_context() {
    HOOK_CONTEXT.with(|ctx| {
        *ctx.borrow_mut() = None;
    });
}

/// Get the current hook context
fn with_hook_context<R>(f: impl FnOnce(&HookContext) -> R) -> R {
    HOOK_CONTEXT.with(|ctx| {
        let ctx_ref = ctx.borrow();
        let context = ctx_ref
            .as_ref()
            .expect("Hook called outside of component render");
        f(context)
    })
}
